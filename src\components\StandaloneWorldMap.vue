<template>
  <div class="standalone-world-map" :style="containerStyle">
    <div class="map-title" v-if="showTitle">
      <span class="title-text">{{ title }}</span>
    </div>
    <div :id="mapId" class="map-container" :class="{ 'show-grid': showGrid }" :style="mapContainerStyle"></div>
  </div>
</template>

<script>
import mapboxgl from 'mapbox-gl'

export default {
  name: 'StandaloneWorldMap',
  props: {
    // Map configuration
    width: {
      type: [String, Number],
      default: '100%'
    },
    height: {
      type: [String, Number],
      default: '500px'
    },
    title: {
      type: String,
      default: 'Global Distribution'
    },
    showTitle: {
      type: Boolean,
      default: true
    },

    // Map behavior
    center: {
      type: Array,
      default: () => [20, 0]
    },
    zoom: {
      type: Number,
      default: 2
    },
    // 区域模式配置 (Regional mode configuration)
    regionalMode: {
      type: <PERSON>olean,
      default: false
    },
    // 区域配置对象 (Regional configuration object)
    regionConfig: {
      type: Object,
      default: () => ({
        name: 'global',
        center: [20, 0],
        zoom: 2,
        bounds: null
      })
    },

    // Path data
    pathData: {
      type: Array,
      default: () => []
    },

    // Styling options
    colors: {
      type: Object,
      default: () => ({
        land: 'rgb(30, 65, 51)',
        borders: 'rgb(76, 159, 123)',
        ocean: 'rgb(18, 46, 44)',
        pathLine: '#1ED9B5',
        sourcePoint: '#1ED9B5',
        targetPoint: '#1ED9B5'
      })
    },

    // Animation settings
    animationEnabled: {
      type: Boolean,
      default: true
    },
    animationSpeed: {
      type: Number,
      default: 2000
    },
    flowAnimationSpeed: {
      type: Number,
      default: 0.002, // Speed of flow particles along paths (further reduced for slower, more deliberate animation)
      validator: (value) => value > 0 && value <= 0.1
    },
    flowParticleCount: {
      type: Number,
      default: 1, // Number of flow particles per path (single particle for cleaner effect)
      validator: (value) => value >= 1 && value <= 10
    },

    // Grid overlay
    showGrid: {
      type: Boolean,
      default: true
    },

    // 最小化模式 - 隐藏文本标签和边界 (Minimalist mode - hide text labels and borders)
    minimalistMode: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      map: null,
      pathLines: [],
      sourceMarkers: [],
      targetMarkers: [],
      flowAnimations: [], // Store flow animation data
      mapboxLoaded: false,
      mapId: `world-map-${Math.random().toString(36).substr(2, 9)}`,
      // Mapbox GL JS 配置 (Mapbox GL JS configuration)
      mapboxAccessToken: 'pk.eyJ1IjoiYmFyNXhjIiwiYSI6ImNtYXdhdzcwZDBiajUydHIycjh0OXZrYW8ifQ.1pfjx8FkKHbR4n94jINJNw',
      mapboxStyleUrl: 'mapbox://styles/bar5xc/cmbou6n4400ny01s65spx6eky',
      // 动画和图层管理 (Animation and layer management)
      animationFrameIds: [],
      pathSources: new Map(),
      markerElements: new Map(),
      // 流星轨迹相关数据 (Meteor trail related data)
      trailLayers: new Map(), // 存储每个粒子的轨迹图层 (Store trail layers for each particle)
      trailSegments: new Map(), // 存储轨迹段数据 (Store trail segment data)
      trailLength: 0.12 // 轨迹长度占总路径的比例 (Trail length as percentage of total path, 12%)
    }
  },
  computed: {
    containerStyle() {
      return {
        width: typeof this.width === 'number' ? `${this.width}px` : this.width,
        height: typeof this.height === 'number' ? `${this.height}px` : this.height
      }
    },
    mapContainerStyle() {
      return {
        backgroundColor: this.colors.ocean
      }
    },
    // 计算实际使用的地图中心点 (Calculate actual map center to use)
    // Mapbox uses [longitude, latitude] format
    actualMapCenter() {
      const center = this.regionalMode && this.regionConfig.center
        ? this.regionConfig.center
        : this.center
      // Convert from [lat, lng] to [lng, lat] for Mapbox
      return [center[1], center[0]]
    },
    // 计算实际使用的缩放级别 (Calculate actual zoom level to use)
    actualMapZoom() {
      return this.regionalMode && this.regionConfig.zoom
        ? this.regionConfig.zoom
        : this.zoom
    }
  },
  mounted() {
    this.initMapbox()
  },
  beforeDestroy() {
    this.cleanup()
  },
  watch: {
    pathData: {
      handler() {
        if (this.map && this.mapboxLoaded) {
          this.renderPaths()
        }
      },
      deep: true
    },
    animationEnabled: {
      handler(newValue) {
        if (!newValue) {
          // Stop all flow animations when animation is disabled
          this.flowAnimations.forEach(animationData => {
            if (animationData.animationFrameId) {
              cancelAnimationFrame(animationData.animationFrameId)
              animationData.animationFrameId = null
            }
            animationData.isActive = false
          })
        } else if (this.map && this.mapboxLoaded) {
          // Restart animations when re-enabled
          this.renderPaths()
        }
      }
    },
    // 监听区域模式变化 (Watch for regional mode changes)
    regionalMode: {
      handler() {
        if (this.map && this.mapboxLoaded) {
          // 重新设置地图视图 (Reset map view)
          this.updateMapView()
        }
      }
    },

    // 监听简洁模式变化 (Watch for minimalist mode changes)
    minimalistMode: {
      handler() {
        if (this.map && this.mapboxLoaded) {
          console.log('简洁模式切换 (Minimalist mode toggled):', this.minimalistMode)
          // 重新渲染路径 (Re-render paths)
          this.renderPaths()
        }
      }
    },
    // 监听区域配置变化 (Watch for region config changes)
    regionConfig: {
      handler() {
        if (this.map && this.mapboxLoaded && this.regionalMode) {
          // 更新地图视图 (Update map view)
          this.updateMapView()
        }
      },
      deep: true
    }
  },
  methods: {
    /**
     * 初始化Mapbox GL JS地图 (Initialize Mapbox GL JS map)
     */
    initMapbox() {
      console.log('开始初始化Mapbox地图 (Starting Mapbox map initialization)')
      console.log('访问令牌 (Access token):', this.mapboxAccessToken.substring(0, 20) + '...')
      console.log('样式URL (Style URL):', this.mapboxStyleUrl)
      console.log('地图中心点 (Map center):', this.actualMapCenter)
      console.log('缩放级别 (Zoom level):', this.actualMapZoom)

      // 设置Mapbox访问令牌 (Set Mapbox access token)
      mapboxgl.accessToken = this.mapboxAccessToken

      // 确保Mapbox CSS已加载 (Ensure Mapbox CSS is loaded)
      this.loadMapboxCSS()

      try {
        // 创建地图实例 - 让Mapbox样式处理默认视图设置 (Create map instance - let Mapbox style handle default view settings)
        this.map = new mapboxgl.Map({
          container: this.mapId,
          style: this.mapboxStyleUrl,
          // 不设置center和zoom，让样式配置处理 (Don't set center and zoom, let style configuration handle it)
          interactive: false, // 禁用交互以匹配原始行为 (Disable interaction to match original behavior)
          attributionControl: false,
          logoPosition: 'bottom-right'
        })
        console.log('Mapbox地图实例创建成功 (Mapbox map instance created successfully)')
      } catch (error) {
        console.error('创建Mapbox地图实例失败 (Failed to create Mapbox map instance):', error)
        return
      }

      // 地图加载完成后的处理 (Handle map load completion)
      this.map.on('load', () => {
        console.log('Mapbox地图加载完成 (Mapbox map loaded)')
        this.mapboxLoaded = true

        // 记录当前地图视图信息 (Log current map view info)
        console.log('地图当前中心点 (Current map center):', this.map.getCenter())
        console.log('地图当前缩放级别 (Current map zoom):', this.map.getZoom())

        // 只在明确需要时设置边界（区域模式且有边界配置）(Only set bounds when explicitly needed)
        if (this.regionalMode && this.regionConfig.bounds) {
          this.applyRegionalBounds()
        }

        // 渲染路径数据 (Render path data)
        if (this.pathData.length > 0) {
          this.renderPaths()
        }

        // 发出地图就绪事件 (Emit map ready event)
        this.$emit('map-ready', this.map)
      })

      // 错误处理 (Error handling)
      this.map.on('error', (e) => {
        console.error('Mapbox地图加载错误 (Mapbox map load error):', e)
        console.error('错误详情 (Error details):', {
          error: e.error,
          sourceId: e.sourceId,
          isSourceLoaded: e.isSourceLoaded
        })
      })

      // 样式加载事件 (Style load events)
      this.map.on('styledata', () => {
        console.log('Mapbox样式数据加载完成 (Mapbox style data loaded)')
      })

      this.map.on('sourcedataloading', (e) => {
        console.log('Mapbox数据源开始加载 (Mapbox source data loading):', e.sourceId)
      })

      this.map.on('sourcedata', (e) => {
        console.log('Mapbox数据源加载完成 (Mapbox source data loaded):', e.sourceId)
      })

      // 添加点击事件用于调试 (Add click event for debugging)
      this.map.on('click', (e) => {
        console.log('地图点击位置 (Map click position):', e.lngLat)
      })
    },

    /**
     * 加载Mapbox CSS样式 (Load Mapbox CSS styles)
     */
    loadMapboxCSS() {
      if (!document.querySelector('link[href*="mapbox-gl.css"]')) {
        const mapboxCss = document.createElement('link')
        mapboxCss.rel = 'stylesheet'
        mapboxCss.href = 'https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.css'
        document.head.appendChild(mapboxCss)
      }
    },

    /**
     * 渲染路径数据到Mapbox地图 (Render path data to Mapbox map)
     */
    renderPaths() {
      if (!this.map || !this.mapboxLoaded) return

      console.log('开始渲染路径 (Starting to render paths):', this.pathData.length)

      // 清除现有的路径和动画 (Clear existing paths and animations)
      this.clearMapLayers()

      // 为每条路径创建数据源和图层 (Create data sources and layers for each path)
      this.pathData.forEach((path, index) => {
        this.renderSinglePath(path, index)
      })
    },

    /**
     * 渲染单条路径到Mapbox地图 (Render single path to Mapbox map)
     */
    renderSinglePath(path, index) {
      if (!path.coords || path.coords.length < 2) {
        console.warn(`路径 ${index} 坐标无效 (Path ${index} has invalid coordinates):`, path)
        return
      }

      const [sourceCoords, targetCoords] = path.coords
      const pathId = `path-${index}`
      const sourceId = `source-${index}`
      const targetId = `target-${index}`

      console.log(`渲染路径 ${index} (Rendering path ${index}):`, {
        name: path.name,
        sourceCoords,
        targetCoords,
        pathId
      })

      // 生成曲线路径点 (Generate curved path points)
      const curvePoints = this.generateCurvePoints(sourceCoords, targetCoords)

      // 转换坐标格式为Mapbox格式 [lng, lat] (Convert coordinates to Mapbox format [lng, lat])
      const mapboxCurvePoints = curvePoints.map(point => [point[1], point[0]])

      console.log(`路径 ${index} 曲线点数量 (Path ${index} curve points count):`, mapboxCurvePoints.length)

      // 创建路径线的GeoJSON数据 (Create GeoJSON data for path line)
      const pathGeoJSON = {
        type: 'Feature',
        geometry: {
          type: 'LineString',
          coordinates: mapboxCurvePoints
        },
        properties: {
          name: path.name || `Path ${index}`,
          value: path.value || 50
        }
      }

      try {
        // 添加路径数据源 (Add path data source)
        if (!this.map.getSource(pathId)) {
          this.map.addSource(pathId, {
            type: 'geojson',
            data: pathGeoJSON
          })
          console.log(`路径数据源已添加 (Path data source added): ${pathId}`)
        }

        // 添加隐藏的路径线图层 (Add hidden path line layer for meteor trail base)
        if (!this.map.getLayer(`${pathId}-line`)) {
          this.map.addLayer({
            id: `${pathId}-line`,
            type: 'line',
            source: pathId,
            layout: {
              'line-join': 'round',
              'line-cap': 'round'
            },
            paint: {
              'line-color': this.colors.pathLine,
              'line-width': 2,
              'line-opacity': 0 // 隐藏静态线条，只显示流星轨迹 (Hide static lines, only show meteor trails)
            }
          })
          console.log(`隐藏路径线图层已添加 (Hidden path line layer added): ${pathId}-line`)

          // 添加隐藏的发光效果 (Add hidden glow effect)
          this.map.addLayer({
            id: `${pathId}-glow`,
            type: 'line',
            source: pathId,
            layout: {
              'line-join': 'round',
              'line-cap': 'round'
            },
            paint: {
              'line-color': this.colors.pathLine,
              'line-width': 6,
              'line-opacity': 0, // 隐藏静态发光，只显示流星轨迹发光 (Hide static glow, only show meteor trail glow)
              'line-blur': 2
            }
          }, `${pathId}-line`) // 将发光层放在线条层下方 (Place glow layer below line layer)
          console.log(`隐藏路径发光图层已添加 (Hidden path glow layer added): ${pathId}-glow`)
        }
      } catch (error) {
        console.error(`添加路径图层失败 (Failed to add path layers) ${pathId}:`, error)
        return
      }

      // 创建起点和终点标记 (Create source and target markers)
      this.createMarker(sourceCoords, sourceId, 'source', path, index)
      this.createMarker(targetCoords, targetId, 'target', path, index)

      // 存储路径信息 (Store path information)
      this.pathSources.set(pathId, {
        sourceId: pathId,
        layerIds: [`${pathId}-line`, `${pathId}-glow`],
        curvePoints: curvePoints,
        pathData: path
      })

      // 如果启用动画，创建流动动画 (If animation enabled, create flow animation)
      if (this.animationEnabled) {
        this.createFlowAnimation(curvePoints, index, pathId)
      }
    },

    /**
     * 创建标记点 (Create marker)
     */
    createMarker(coords, markerId, type, pathData, index) {
      // 创建标记元素 (Create marker element)
      const markerElement = document.createElement('div')
      markerElement.className = `marker ${type}-marker`
      markerElement.style.cssText = `
        width: ${Math.max(8, (pathData.value || 50) / 30)}px;
        height: ${Math.max(8, (pathData.value || 50) / 30)}px;
        background-color: ${this.colors[type + 'Point']};
        border: 2px solid ${this.colors[type + 'Point']};
        border-radius: 50%;
        box-shadow: 0 0 10px ${this.colors[type + 'Point']};
        cursor: pointer;
        transition: all 0.3s ease;
      `

      // 创建Mapbox标记 (Create Mapbox marker)
      const marker = new mapboxgl.Marker(markerElement)
        .setLngLat([coords[1], coords[0]]) // Mapbox uses [lng, lat]
        .addTo(this.map)

      // 添加点击事件和提示信息 (Add click event and tooltip)
      if (pathData.name) {
        const tooltipText = type === 'source'
          ? pathData.name.split(' to ')[0] || 'Source'
          : pathData.name.split(' to ')[1] || 'Target'

        markerElement.title = tooltipText
      }

      // 存储标记引用 (Store marker reference)
      this.markerElements.set(markerId, {
        marker: marker,
        element: markerElement,
        type: type,
        coords: coords,
        pathData: pathData
      })

      // 如果启用动画，开始标记动画 (If animation enabled, start marker animation)
      if (this.animationEnabled) {
        this.animateMarker(markerElement, index + (type === 'target' ? 0.5 : 0))
      }

      return marker
    },

    /**
     * 生成曲线路径点 (Generate curved path points)
     */
    generateCurvePoints(start, end, numPoints = 20) {
      const points = []

      // Calculate the midpoint
      const midLat = (start[0] + end[0]) / 2
      const midLng = (start[1] + end[1]) / 2

      // Calculate the distance for curve height
      const distance = Math.sqrt(
        Math.pow(end[0] - start[0], 2) + Math.pow(end[1] - start[1], 2)
      )

      // Add curvature based on distance
      const curvature = distance * 0.3

      // Determine if we should curve up or down based on hemisphere
      const curveDirection = midLat > 0 ? 1 : -1
      const controlPoint = [midLat + (curvature * curveDirection), midLng]

      // Generate points along the quadratic bezier curve
      for (let i = 0; i <= numPoints; i++) {
        const t = i / numPoints
        const lat = Math.pow(1 - t, 2) * start[0] + 2 * (1 - t) * t * controlPoint[0] + Math.pow(t, 2) * end[0]
        const lng = Math.pow(1 - t, 2) * start[1] + 2 * (1 - t) * t * controlPoint[1] + Math.pow(t, 2) * end[1]
        points.push([lat, lng])
      }

      return points
    },

    /**
     * 动画标记点 (Animate marker)
     */
    animateMarker(markerElement, index) {
      if (!markerElement || !this.animationEnabled) return

      let opacity = 0.3
      let increasing = true

      const animate = () => {
        if (increasing) {
          opacity += 0.015
          if (opacity >= 1) {
            increasing = false
          }
        } else {
          opacity -= 0.015
          if (opacity <= 0.3) {
            increasing = true
          }
        }

        // 更新标记元素的透明度和发光效果 (Update marker element opacity and glow effect)
        markerElement.style.opacity = opacity
        markerElement.style.boxShadow = `0 0 ${10 + opacity * 10}px ${markerElement.style.backgroundColor}`

        // 继续动画如果标记仍然有效且动画已启用 (Continue animation if marker is still valid and animation is enabled)
        if (markerElement.parentNode && this.animationEnabled) {
          const animationId = requestAnimationFrame(animate)
          this.animationFrameIds.push(animationId)
        }
      }

      // 根据索引延迟开始动画 (Start animation with delay based on index)
      setTimeout(() => {
        if (this.animationEnabled && markerElement.parentNode) {
          const animationId = requestAnimationFrame(animate)
          this.animationFrameIds.push(animationId)
        }
      }, index * 150)
    },

    /**
     * 创建流星轨迹动画 (Create meteor trail animation)
     */
    createFlowAnimation(curvePoints, pathIndex, pathId) {
      if (!this.map || !this.animationEnabled || curvePoints.length < 2) return

      // 创建多个流动粒子以获得更好的视觉效果 (Create multiple flow particles for better visual effect)
      const numParticles = this.flowParticleCount
      const particles = []

      for (let i = 0; i < numParticles; i++) {
        // 创建粒子元素 (Create particle element)
        const particleElement = document.createElement('div')
        particleElement.className = 'flow-particle'
        // 使用标准颜色粒子 (Use standard color particles)
        const particleColor = 'hsl(168, 76%, 48%)' // #1ED9B5 标准颜色 (Standard color #1ED9B5)
        particleElement.style.cssText = `
          width: 6px;
          height: 6px;
          background-color: ${particleColor};
          border: 1px solid ${particleColor};
          border-radius: 50%;
          box-shadow: 0 0 8px ${particleColor};
          position: absolute;
          pointer-events: none;
          z-index: 1000;
        `

        // 创建Mapbox标记 (Create Mapbox marker)
        const particleMarker = new mapboxgl.Marker(particleElement)
          .setLngLat([curvePoints[0][1], curvePoints[0][0]]) // 从起点开始 (Start from beginning)
          .addTo(this.map)

        // 为每个粒子创建轨迹数据 (Create trail data for each particle)
        const particleId = `${pathId}-particle-${i}`
        const trailData = {
          particleId: particleId,
          trailSegments: [], // 存储轨迹段 (Store trail segments)
          maxTrailSegments: Math.ceil(curvePoints.length * this.trailLength), // 最大轨迹段数 (Max trail segments)
          layerIds: [] // 存储图层ID (Store layer IDs)
        }

        particles.push({
          marker: particleMarker,
          element: particleElement,
          progress: 0, // 所有粒子从起点开始，实现顺序流动 (All particles start from beginning for sequential flow)
          pathIndex: pathIndex,
          isActive: i === 0, // 只有第一个粒子开始时是活跃的 (Only first particle is active initially)
          trailData: trailData
        })

        // 存储轨迹数据 (Store trail data)
        this.trailSegments.set(particleId, trailData)
      }

      // 动画状态 (Animation state)
      const animationData = {
        particles: particles,
        curvePoints: curvePoints,
        pathIndex: pathIndex,
        pathId: pathId,
        animationFrameId: null,
        isActive: true
      }

      this.flowAnimations.push(animationData)

      // 动画函数 (Animation function)
      const animateFlow = () => {
        // 检查动画状态 (Check animation state)
        if (!this.animationEnabled || !animationData.isActive || !this.map) {
          return
        }

        let allParticlesValid = true
        let hasValidParticles = false

        // 实现顺序粒子流动：一次只有一个粒子活跃 (Implement sequential particle flow: only one particle active at a time)
        let activeParticleIndex = -1
        let currentActiveParticle = null

        // 找到当前活跃的粒子 (Find currently active particle)
        for (let i = 0; i < animationData.particles.length; i++) {
          const particle = animationData.particles[i]
          if (particle && particle.isActive) {
            activeParticleIndex = i
            currentActiveParticle = particle
            hasValidParticles = true
            break
          }
        }

        // 如果没有活跃粒子，激活第一个 (If no active particle, activate the first one)
        if (currentActiveParticle === null && animationData.particles.length > 0) {
          const firstParticle = animationData.particles[0]
          if (firstParticle && firstParticle.marker) {
            firstParticle.isActive = true
            firstParticle.progress = 0
            currentActiveParticle = firstParticle
            activeParticleIndex = 0
            hasValidParticles = true
          }
        }

        // 处理当前活跃的粒子 (Process the currently active particle)
        if (currentActiveParticle && currentActiveParticle.marker && currentActiveParticle.element) {
          try {
            // 检查粒子元素是否仍在DOM中 (Check if particle element is still in DOM)
            const isInDOM = currentActiveParticle.element.parentNode || document.contains(currentActiveParticle.element)

            if (!isInDOM) {
              console.warn('粒子元素不在DOM中 (Particle element not in DOM):', currentActiveParticle.trailData?.particleId)
              allParticlesValid = false
            } else {
              // 更新粒子进度 (Update particle progress)
              currentActiveParticle.progress += this.flowAnimationSpeed

              // 当粒子完成路径时，切换到下一个粒子 (When particle completes path, switch to next particle)
              if (currentActiveParticle.progress >= 1) {

                // 隐藏当前粒子 (Hide current particle)
                currentActiveParticle.element.style.opacity = '0'
                currentActiveParticle.isActive = false
                currentActiveParticle.progress = 0

                // 清理当前粒子的轨迹段 (Cleanup current particle's trail segments)
                if (currentActiveParticle.trailData && currentActiveParticle.trailData.trailSegments) {
                  currentActiveParticle.trailData.trailSegments.forEach(segment => {
                    this.cleanupTrailSegment(segment)
                  })
                  currentActiveParticle.trailData.trailSegments = []
                }

                // 激活下一个粒子（循环到第一个）(Activate next particle, cycling back to first)
                const nextIndex = (activeParticleIndex + 1) % animationData.particles.length
                const nextParticle = animationData.particles[nextIndex]

                // 立即激活下一个粒子，而不是使用延迟 (Immediately activate next particle instead of using delay)
                if (nextParticle && nextParticle.marker && animationData.isActive && this.animationEnabled) {
                  nextParticle.isActive = true
                  nextParticle.progress = 0
                  nextParticle.element.style.opacity = '1'
                }
              } else {
                // 计算沿曲线的位置 (Calculate position along the curve)
                const position = this.getPositionAlongPath(curvePoints, currentActiveParticle.progress)
                if (position) {
                  // 更新粒子位置 (Update particle position)
                  currentActiveParticle.marker.setLngLat([position[1], position[0]]) // Mapbox uses [lng, lat]

                  // 根据进度添加淡入淡出效果 (Add fade effect based on progress)
                  const opacity = Math.sin(currentActiveParticle.progress * Math.PI) * 0.7 + 0.3
                  currentActiveParticle.element.style.opacity = opacity
                  // 使用标准颜色粒子的发光效果 (Use standard color particle glow effect)
                  const particleColor = 'hsl(168, 76%, 48%)' // #1ED9B5
                  currentActiveParticle.element.style.boxShadow = `0 0 ${8 * opacity}px ${particleColor}`

                  // 更新流星轨迹 (Update meteor trail)
                  this.updateParticleTrail(currentActiveParticle, curvePoints)
                } else {
                  console.warn('无法计算粒子位置 (Cannot calculate particle position):', currentActiveParticle.progress)
                }
              }
            }
          } catch (e) {
            // 优雅地处理潜在错误 (Handle potential errors gracefully)
            console.warn('粒子处理错误 (Particle processing error):', e, currentActiveParticle.trailData?.particleId)
            allParticlesValid = false
          }
        } else {
          console.warn('当前活跃粒子无效 (Current active particle invalid):', {
            hasParticle: !!currentActiveParticle,
            hasMarker: !!(currentActiveParticle && currentActiveParticle.marker),
            hasElement: !!(currentActiveParticle && currentActiveParticle.element)
          })
          allParticlesValid = false
        }

        // 隐藏所有非活跃粒子 (Hide all inactive particles)
        animationData.particles.forEach((particle, index) => {
          if (particle && particle.element && !particle.isActive) {
            try {
              particle.element.style.opacity = '0'
            } catch (e) {
              console.warn(`隐藏粒子失败 (Failed to hide particle) ${index}:`, e)
            }
          }
        })

        // 检查是否应该继续动画 (Check if animation should continue)
        const shouldContinue = this.animationEnabled &&
          animationData.isActive &&
          this.map &&
          (allParticlesValid || hasValidParticles)

        if (shouldContinue) {
          // 继续动画循环 (Continue animation loop)
          animationData.animationFrameId = requestAnimationFrame(animateFlow)
        } else {
          // 清理动画 (Cleanup animation)
          this.cleanupFlowAnimation(animationData)
        }
      }

      // 根据路径索引延迟开始动画 (Start the animation with a delay based on path index)
      const startDelay = pathIndex * 200

      setTimeout(() => {
        if (this.animationEnabled && animationData.isActive && this.map) {
          animationData.animationFrameId = requestAnimationFrame(animateFlow)
        }
      }, startDelay)
    },

    /**
     * 创建流星轨迹段 (Create meteor trail segment)
     */
    createTrailSegment(particleId, startProgress, endProgress, curvePoints, opacity) {
      if (!this.map || startProgress >= endProgress || !curvePoints.length) return null

      // 计算轨迹段的路径点 (Calculate trail segment path points)
      const segmentPoints = []
      const totalPoints = curvePoints.length
      const startIndex = Math.floor(startProgress * (totalPoints - 1))
      const endIndex = Math.ceil(endProgress * (totalPoints - 1))

      for (let i = startIndex; i <= Math.min(endIndex, totalPoints - 1); i++) {
        const point = curvePoints[i]
        segmentPoints.push([point[1], point[0]]) // 转换为Mapbox格式 [lng, lat] (Convert to Mapbox format [lng, lat])
      }

      if (segmentPoints.length < 2) return null

      // 创建轨迹段的GeoJSON数据 (Create GeoJSON data for trail segment)
      const trailGeoJSON = {
        type: 'Feature',
        geometry: {
          type: 'LineString',
          coordinates: segmentPoints
        },
        properties: {
          opacity: opacity
        }
      }

      const segmentId = `${particleId}-trail-${Date.now()}-${Math.random()}`

      try {
        // 添加轨迹段数据源 (Add trail segment data source)
        this.map.addSource(segmentId, {
          type: 'geojson',
          data: trailGeoJSON
        })

        // 添加轨迹段图层 (Add trail segment layer)
        this.map.addLayer({
          id: `${segmentId}-line`,
          type: 'line',
          source: segmentId,
          layout: {
            'line-join': 'round',
            'line-cap': 'round'
          },
          paint: {
            'line-color': this.colors.pathLine,
            'line-width': 2,
            'line-opacity': opacity
          }
        })

        // 添加轨迹段发光效果 (Add trail segment glow effect)
        this.map.addLayer({
          id: `${segmentId}-glow`,
          type: 'line',
          source: segmentId,
          layout: {
            'line-join': 'round',
            'line-cap': 'round'
          },
          paint: {
            'line-color': 'hsl(168, 76%, 48%)', // #1ED9B5 发光效果 (Standard color glow effect)
            'line-width': 6,
            'line-opacity': opacity * 0.5,
            'line-blur': 2
          }
        }, `${segmentId}-line`)

        return {
          segmentId: segmentId,
          layerIds: [`${segmentId}-line`, `${segmentId}-glow`],
          opacity: opacity
        }
      } catch (error) {
        console.warn('创建轨迹段失败 (Failed to create trail segment):', error)
        return null
      }
    },

    /**
     * 清理轨迹段 (Cleanup trail segment)
     */
    cleanupTrailSegment(segmentInfo) {
      if (!segmentInfo || !this.map) return

      try {
        // 移除图层 (Remove layers)
        if (segmentInfo.layerIds && Array.isArray(segmentInfo.layerIds)) {
          segmentInfo.layerIds.forEach(layerId => {
            try {
              if (this.map.getLayer(layerId)) {
                this.map.removeLayer(layerId)
              }
            } catch (e) {
              console.warn(`移除轨迹图层失败 (Failed to remove trail layer) ${layerId}:`, e)
            }
          })
        }

        // 移除数据源 (Remove data source)
        if (segmentInfo.segmentId) {
          try {
            if (this.map.getSource(segmentInfo.segmentId)) {
              this.map.removeSource(segmentInfo.segmentId)
            }
          } catch (e) {
            console.warn(`移除轨迹数据源失败 (Failed to remove trail data source) ${segmentInfo.segmentId}:`, e)
          }
        }
      } catch (error) {
        console.warn('清理轨迹段时发生错误 (Error during trail segment cleanup):', error)
      }
    },

    /**
     * 更新粒子轨迹 (Update particle trail)
     */
    updateParticleTrail(particle, curvePoints) {
      if (!particle.trailData || !this.map) return

      const trailData = particle.trailData
      const currentProgress = particle.progress
      const trailLength = this.trailLength

      // 计算轨迹起始位置 (Calculate trail start position)
      const trailStartProgress = Math.max(0, currentProgress - trailLength)

      // 清理过期的轨迹段 (Cleanup expired trail segments)
      trailData.trailSegments = trailData.trailSegments.filter(segment => {
        if (segment.endProgress < trailStartProgress) {
          this.cleanupTrailSegment(segment)
          return false
        }
        return true
      })

      // 只在进度变化足够大时更新轨迹段，避免过度创建 (Only update trail segments when progress changes significantly)
      const progressThreshold = 0.05 // 5% 进度变化阈值 (5% progress change threshold)
      const lastSegment = trailData.trailSegments[trailData.trailSegments.length - 1]

      if (currentProgress > progressThreshold && trailStartProgress < currentProgress &&
        (!lastSegment || currentProgress - lastSegment.endProgress >= progressThreshold)) {

        // 清理所有现有轨迹段，重新创建完整轨迹 (Clear all existing segments and recreate complete trail)
        trailData.trailSegments.forEach(segment => {
          this.cleanupTrailSegment(segment)
        })
        trailData.trailSegments = []

        // 创建单个完整的轨迹段，使用渐变透明度 (Create single complete trail segment with gradient opacity)
        const segmentInfo = this.createTrailSegment(
          trailData.particleId,
          trailStartProgress,
          currentProgress,
          curvePoints,
          0.7 // 基础透明度 (Base opacity)
        )

        if (segmentInfo) {
          trailData.trailSegments.push({
            ...segmentInfo,
            startProgress: trailStartProgress,
            endProgress: currentProgress
          })
        }
      }
    },

    /**
     * 获取沿路径的位置 (Get position along path)
     */
    getPositionAlongPath(points, progress) {
      if (!points || points.length < 2 || progress < 0 || progress > 1) {
        return null
      }

      // 将进度限制在有效范围内 (Clamp progress to valid range)
      progress = Math.max(0, Math.min(1, progress))

      // 计算沿路径的确切位置 (Calculate the exact position along the path)
      const totalSegments = points.length - 1
      const segmentProgress = progress * totalSegments
      const segmentIndex = Math.floor(segmentProgress)
      const localProgress = segmentProgress - segmentIndex

      // 处理进度恰好为1的边缘情况 (Handle edge case where progress is exactly 1)
      if (segmentIndex >= totalSegments) {
        return points[points.length - 1]
      }

      // 在两点之间插值 (Interpolate between two points)
      const startPoint = points[segmentIndex]
      const endPoint = points[segmentIndex + 1]

      const lat = startPoint[0] + (endPoint[0] - startPoint[0]) * localProgress
      const lng = startPoint[1] + (endPoint[1] - startPoint[1]) * localProgress

      return [lat, lng]
    },

    /**
     * 清理流动动画 (Cleanup flow animation)
     */
    cleanupFlowAnimation(animationData) {
      if (!animationData) return



      // 取消动画帧 (Cancel animation frame)
      if (animationData.animationFrameId) {
        cancelAnimationFrame(animationData.animationFrameId)
        animationData.animationFrameId = null
      }

      // 从地图中移除粒子和轨迹 (Remove particles and trails from map)
      animationData.particles.forEach((particle, index) => {
        if (particle) {
          // 移除粒子标记 (Remove particle marker)
          if (particle.marker && this.map) {
            try {
              particle.marker.remove()

            } catch (e) {
              console.warn(`移除粒子标记失败 (Failed to remove particle marker) ${index}:`, e)
            }
          }

          // 隐藏粒子元素 (Hide particle element)
          if (particle.element) {
            try {
              particle.element.style.opacity = '0'
              particle.element.style.display = 'none'
            } catch (e) {
              console.warn(`隐藏粒子元素失败 (Failed to hide particle element) ${index}:`, e)
            }
          }

          // 清理粒子的轨迹段 (Cleanup particle trail segments)
          if (particle.trailData && particle.trailData.trailSegments) {
            particle.trailData.trailSegments.forEach(segment => {
              this.cleanupTrailSegment(segment)
            })
            particle.trailData.trailSegments = []
          }

          // 重置粒子状态 (Reset particle state)
          particle.isActive = false
          particle.progress = 0
        }
      })

      // 标记为非活动状态 (Mark as inactive)
      animationData.isActive = false
    },

    /**
     * 清理地图图层 (Clear map layers)
     */
    clearMapLayers() {
      // 清理所有动画帧 (Clear all animation frames)
      this.animationFrameIds.forEach(id => {
        cancelAnimationFrame(id)
      })
      this.animationFrameIds = []

      // 清理路径数据源和图层 (Clear path data sources and layers)
      this.pathSources.forEach((pathInfo, pathId) => {
        // 移除图层 (Remove layers)
        pathInfo.layerIds.forEach(layerId => {
          if (this.map.getLayer(layerId)) {
            this.map.removeLayer(layerId)
          }
        })

        // 移除数据源 (Remove data source)
        if (this.map.getSource(pathId)) {
          this.map.removeSource(pathId)
        }
      })
      this.pathSources.clear()

      // 清理标记 (Clear markers)
      this.markerElements.forEach((markerInfo) => {
        if (markerInfo.marker) {
          markerInfo.marker.remove()
        }
      })
      this.markerElements.clear()

      // 清理流动动画 (Clear flow animations)
      this.flowAnimations.forEach(animationData => {
        this.cleanupFlowAnimation(animationData)
      })
      this.flowAnimations = []

      // 清理轨迹相关数据 (Clear trail related data)
      this.trailLayers.clear()
      this.trailSegments.clear()

      // 清理旧的数组（向后兼容）(Clear old arrays for backward compatibility)
      this.pathLines = []
      this.sourceMarkers = []
      this.targetMarkers = []
    },

    /**
     * 组件清理 (Component cleanup)
     */
    cleanup() {
      // 清理所有图层 (Clear all layers)
      this.clearMapLayers()

      // 移除地图 (Remove map)
      if (this.map) {
        this.map.remove()
        this.map = null
      }

      // 重置状态 (Reset state)
      this.mapboxLoaded = false
    },

    /**
     * 应用区域边界限制 (Apply regional bounds restrictions)
     */
    applyRegionalBounds() {
      if (!this.map || !this.mapboxLoaded || !this.regionConfig.bounds) return

      try {
        const bounds = this.regionConfig.bounds
        const mapboxBounds = [
          [bounds.west, bounds.south], // 西南角 (Southwest corner)
          [bounds.east, bounds.north]  // 东北角 (Northeast corner)
        ]
        this.map.setMaxBounds(mapboxBounds)
        console.log('区域边界已应用 (Regional bounds applied):', bounds)
      } catch (error) {
        console.error('应用区域边界失败 (Failed to apply regional bounds):', error)
      }
    },

    /**
     * 更新地图视图 - 仅在模式切换时使用 (Update map view - only used when switching modes)
     */
    updateMapView() {
      if (!this.map || !this.mapboxLoaded) return

      try {
        // 只在明确切换模式时才改变视图 (Only change view when explicitly switching modes)
        if (this.regionalMode && this.regionConfig.center && this.regionConfig.zoom) {
          // 切换到区域模式 (Switch to regional mode)
          this.map.flyTo({
            center: this.actualMapCenter,
            zoom: this.actualMapZoom,
            duration: 1000 // 1秒动画过渡 (1 second animation transition)
          })

          // 应用区域边界 (Apply regional bounds)
          this.applyRegionalBounds()

          console.log(`切换到区域模式 (Switched to regional mode): 中心点(Center) ${this.actualMapCenter}, 缩放级别(Zoom) ${this.actualMapZoom}`)
        } else if (!this.regionalMode) {
          // 切换到全球模式 - 清除边界限制 (Switch to global mode - clear boundary restrictions)
          this.map.setMaxBounds(null)

          // 可以选择飞回到全球视图或保持当前视图 (Can choose to fly back to global view or keep current view)
          // 这里我们保持当前视图，让用户手动调整 (Here we keep current view, let user adjust manually)
          console.log('切换到全球模式 (Switched to global mode)')
        }
      } catch (error) {
        console.error('更新地图视图失败 (Failed to update map view):', error)
      }
    },

    // 公共方法用于外部控制 (Public methods for external control)
    addPath(pathData) {
      const newPaths = Array.isArray(pathData) ? pathData : [pathData]
      this.$emit('update:pathData', [...this.pathData, ...newPaths])
    },

    removePath(index) {
      if (index >= 0 && index < this.pathData.length) {
        const newPaths = [...this.pathData]
        newPaths.splice(index, 1)
        this.$emit('update:pathData', newPaths)
      }
    },

    clearPaths() {
      this.$emit('update:pathData', [])
    },

    /**
     * 设置区域模式 (Set regional mode)
     * @param {Object} regionConfig - 区域配置对象 (Regional configuration object)
     */
    setRegionalMode(regionConfig) {
      // 通过事件通知父组件更新区域模式 (Notify parent component to update regional mode via event)
      this.$emit('update:regionalMode', true)
      this.$emit('update:regionConfig', { ...this.regionConfig, ...regionConfig })
      this.updateMapView()
    },

    /**
     * 切换到全球模式 (Switch to global mode)
     */
    setGlobalMode() {
      // 通过事件通知父组件更新区域模式 (Notify parent component to update regional mode via event)
      this.$emit('update:regionalMode', false)
      this.updateMapView()
    }
  }
}
</script>

<style scoped>
.standalone-world-map {
  position: relative;
  display: flex;
  flex-direction: column;
  background-color: #0F2E2C;
  border-radius: 8px;
  overflow: hidden;
}

.map-title {
  padding: 10px 15px;
  background-color: rgba(0, 0, 0, 0.3);
  border-bottom: 1px solid rgba(0, 255, 204, 0.3);
}

.title-text {
  color: #00FFCC;
  font-size: 16px;
  font-weight: 500;
}

.map-container {
  flex: 1;
  position: relative;
  min-height: 400px;
}

/* Mapbox GL JS 样式覆盖 (Mapbox GL JS style overrides) */
.map-container :deep(.mapboxgl-canvas-container) {
  background-color: #0F2E2C !important;
}

.map-container :deep(.mapboxgl-canvas) {
  background-color: #0F2E2C !important;
}

/* 隐藏Mapbox标志和属性 (Hide Mapbox logo and attribution) */
.map-container :deep(.mapboxgl-ctrl-logo) {
  display: none !important;
}

.map-container :deep(.mapboxgl-ctrl-attrib) {
  display: none !important;
}

/* 标记样式 (Marker styles) */
.map-container :deep(.marker) {
  transition: all 0.3s ease;
}

.map-container :deep(.marker:hover) {
  transform: scale(1.2);
}

/* 流动粒子样式 (Flow particle styles) */
.map-container :deep(.flow-particle) {
  transition: opacity 0.1s ease;
}

/* 网格覆盖层样式 - 仅在启用时显示 (Grid overlay styles - only show when enabled) */
.map-container.show-grid::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(rgba(76, 159, 123, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(76, 159, 123, 0.05) 1px, transparent 1px);
  background-size: 20px 20px;
  pointer-events: none;
  z-index: 10;
}

/* 发光效果 (Glow effect) */
.map-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  box-shadow: inset 0 0 50px rgba(76, 159, 123, 0.1);
  pointer-events: none;
  z-index: 11;
}
</style>
