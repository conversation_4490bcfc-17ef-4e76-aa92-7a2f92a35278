{"version": 8, "name": "Streets-copy", "metadata": {"mapbox:type": "default", "mapbox:trackposition": false, "mapbox:uiParadigm": "layers", "mapbox:decompiler": {"id": "cmbou6n4400ny01s65spx6eky", "componentVersion": "18.0.0", "strata": [{"id": "streets-v12", "order": [["land-and-water", "land"], ["land-and-water", "water"], ["land-and-water", "built"], ["transit", "built"], ["buildings", "built"], ["walking-cycling", "tunnels"], ["road-network", "tunnels"], ["walking-cycling", "surface"], ["road-network", "surface"], ["transit", "surface"], ["walking-cycling", "barriers-bridges"], ["road-network", "bridges"], ["transit", "bridges"], ["buildings", "extruded"], ["admin-boundaries", "admin"], ["land-and-water", "terrain-labels"], ["buildings", "building-labels"], ["road-network", "road-labels"], ["walking-cycling", "walking-cycling-labels"], ["transit", "ferry-aerialway-labels"], ["natural-features", "natural-labels"], ["point-of-interest-labels", "poi-labels"], ["transit", "transit-labels"], ["place-labels", "place-labels"]]}], "overrides": {"land-and-water": {"water": {"paint": {"fill-opacity": 0}}, "water-shadow": {"paint": {"fill-color": "hsla(208, 79%, 50%, 0)"}}, "waterway": {"paint": {"line-color": "hsla(189, 75%, 51%, 0)"}}, "land": {"paint": {"background-opacity": 1, "background-pattern": {"remove": true}, "background-color": "hsl(82, 20%, 100%)"}}, "landcover": {"paint": {"fill-color": ["match", ["get", "class"], "wood", "rgba(148, 238, 140, 0.8)", "snow", "hsl(189, 45%, 76%)", "rgb(186, 239, 175)"]}}, "water-depth": {"paint": {"fill-color": ["interpolate", ["linear"], ["get", "min_depth"], 0, "hsla(189, 75%, 51%, 0)", 200, "hsla(189, 75%, 46%, 0)", 7000, "hsla(189, 75%, 38%, 0)"]}}}, "transit": {"ferry-auto": {"layout": {"visibility": "none"}}, "aerialway": {"layout": {"visibility": "none"}}, "airport-label": {"layout": {"visibility": "none"}}, "aeroway-line": {"layout": {"visibility": "none"}}, "bridge-rail": {"layout": {"visibility": "none"}}, "road-rail-tracks": {"layout": {"visibility": "none"}}, "ferry": {"layout": {"visibility": "none"}}, "transit-label": {"layout": {"visibility": "none"}}, "aeroway-polygon": {"layout": {"visibility": "none"}}, "ferry-aerialway-label": {"layout": {"visibility": "none"}}, "road-rail": {"layout": {"visibility": "none"}}, "bridge-rail-tracks": {"layout": {"visibility": "none"}}}, "natural-features": {"waterway-label": {"layout": {"visibility": "none"}}, "natural-line-label": {"layout": {"visibility": "none"}}, "natural-point-label": {"layout": {"visibility": "none"}}, "water-line-label": {"layout": {"visibility": "none"}}, "water-point-label": {"layout": {"visibility": "none"}}}, "point-of-interest-labels": {"poi-label": {"layout": {"visibility": "none"}}}, "road-network": {"tunnel-primary-case": {"layout": {"visibility": "none"}}, "road-number-shield": {"layout": {"visibility": "none"}}, "tunnel-primary-case-navigation": {"layout": {"visibility": "none"}}, "road-number-shield-navigation": {"layout": {"visibility": "none"}}, "bridge-primary-case-navigation": {"layout": {"visibility": "none"}}, "road-major-link-case": {"layout": {"visibility": "none"}}, "road-construction-navigation": {"layout": {"visibility": "none"}}, "turning-feature-outline": {"layout": {"visibility": "none"}}, "bridge-oneway-arrow-blue-navigation": {"layout": {"visibility": "none"}}, "bridge-primary-navigation": {"layout": {"visibility": "none"}}, "tunnel-primary-navigation": {"layout": {"visibility": "none"}}, "tunnel-oneway-arrow-blue-navigation": {"layout": {"visibility": "none"}}, "bridge-oneway-arrow-white": {"layout": {"visibility": "none"}}, "bridge-motorway-trunk": {"layout": {"visibility": "none"}}, "road-secondary-tertiary-case": {"layout": {"visibility": "none"}}, "tunnel-motorway-trunk": {"layout": {"visibility": "none"}}, "tunnel-minor-link-case": {"layout": {"visibility": "none"}}, "bridge-major-link-2-case": {"layout": {"visibility": "none"}}, "tunnel-oneway-arrow-white": {"layout": {"visibility": "none"}}, "road-major-link-navigation": {"layout": {"visibility": "none"}}, "road-label": {"layout": {"visibility": "none"}}, "bridge-minor-link-case": {"layout": {"visibility": "none"}}, "bridge-street": {"layout": {"visibility": "none"}}, "tunnel-street": {"layout": {"visibility": "none"}}, "road-primary-case": {"layout": {"visibility": "none"}}, "road-minor": {"layout": {"visibility": "none"}}, "bridge-motorway-trunk-case-navigation": {"layout": {"visibility": "none"}}, "traffic-signal-navigation": {"layout": {"visibility": "none"}}, "road-secondary-tertiary-navigation": {"layout": {"visibility": "none"}}, "bridge-major-link-2-navigation": {"layout": {"visibility": "none"}}, "tunnel-motorway-trunk-case-navigation": {"layout": {"visibility": "none"}}, "road-street-low-navigation": {"layout": {"visibility": "none"}}, "tunnel-major-link-case": {"layout": {"visibility": "none"}}, "crosswalks": {"layout": {"visibility": "none"}}, "bridge-motorway-trunk-2": {"layout": {"visibility": "none"}}, "road-motorway-trunk-case-low-navigation": {"layout": {"visibility": "none"}}, "bridge-street-case-navigation": {"layout": {"visibility": "none"}}, "tunnel-construction-navigation": {"layout": {"visibility": "none"}}, "bridge-case-simple": {"layout": {"visibility": "none"}}, "level-crossing": {"layout": {"visibility": "none"}}, "bridge-major-link-case": {"layout": {"visibility": "none"}}, "road-exit-shield": {"layout": {"visibility": "none"}}, "tunnel-street-case-navigation": {"layout": {"visibility": "none"}}, "bridge-construction-navigation": {"layout": {"visibility": "none"}}, "road-primary-navigation": {"layout": {"visibility": "none"}}, "bridge-minor-case": {"layout": {"visibility": "none"}}, "road-oneway-arrow-blue-navigation": {"layout": {"visibility": "none"}}, "road-minor-case-navigation": {"layout": {"visibility": "none"}}, "tunnel-secondary-tertiary-case": {"layout": {"visibility": "none"}}, "tunnel-minor-case": {"layout": {"visibility": "none"}}, "road-polygon": {"layout": {"visibility": "none"}}, "road-street-case": {"layout": {"visibility": "none"}}, "road-oneway-arrow-white": {"layout": {"visibility": "none"}}, "bridge-motorway-trunk-2-case-navigation": {"layout": {"visibility": "none"}}, "bridge-secondary-tertiary-case": {"layout": {"visibility": "none"}}, "tunnel-major-link-navigation": {"layout": {"visibility": "none"}}, "road-motorway-trunk": {"layout": {"visibility": "none"}}, "bridge-street-low": {"layout": {"visibility": "none"}}, "turning-feature": {"layout": {"visibility": "none"}}, "tunnel-street-low": {"layout": {"visibility": "none"}}, "bridge-secondary-tertiary": {"layout": {"visibility": "none"}}, "bridge-major-link-navigation": {"layout": {"visibility": "none"}}, "road-motorway-trunk-case": {"layout": {"visibility": "none"}}, "bridge-minor-navigation": {"layout": {"visibility": "none"}}, "tunnel-minor": {"layout": {"visibility": "none"}}, "tunnel-secondary-tertiary": {"layout": {"visibility": "none"}}, "road-street": {"layout": {"visibility": "none"}}, "bridge-minor": {"layout": {"visibility": "none"}}, "road-street-navigation": {"layout": {"visibility": "none"}}, "tunnel-secondary-tertiary-navigation": {"layout": {"visibility": "none"}}, "tunnel-minor-navigation": {"layout": {"visibility": "none"}}, "bridge-major-link": {"layout": {"visibility": "none"}}, "bridge-secondary-tertiary-navigation": {"layout": {"visibility": "none"}}, "tunnel-street-low-navigation": {"layout": {"visibility": "none"}}, "road-motorway-trunk-case-navigation": {"layout": {"visibility": "none"}}, "bridge-secondary-tertiary-case-navigation": {"layout": {"visibility": "none"}}, "road-oneway-arrow-white-navigation": {"layout": {"visibility": "none"}}, "turning-feature-navigation": {"layout": {"visibility": "none"}}, "bridge-street-low-navigation": {"layout": {"visibility": "none"}}, "tunnel-major-link": {"layout": {"visibility": "none"}}, "road-motorway-trunk-navigation": {"layout": {"visibility": "none"}}, "bridge-motorway-trunk-2-case": {"layout": {"visibility": "none"}}, "road-street-case-navigation": {"layout": {"visibility": "none"}}, "tunnel-minor-case-navigation": {"layout": {"visibility": "none"}}, "tunnel-secondary-tertiary-case-navigation": {"layout": {"visibility": "none"}}, "road-primary": {"layout": {"visibility": "none"}}, "bridge-construction": {"layout": {"visibility": "none"}}, "tunnel-street-case": {"layout": {"visibility": "none"}}, "road-minor-case": {"layout": {"visibility": "none"}}, "bridge-minor-case-navigation": {"layout": {"visibility": "none"}}, "road-oneway-arrow-blue": {"layout": {"visibility": "none"}}, "road-intersection": {"layout": {"visibility": "none"}}, "bridge-simple": {"layout": {"visibility": "none"}}, "level-crossing-navigation": {"layout": {"visibility": "none"}}, "road-exit-shield-navigation": {"layout": {"visibility": "none"}}, "bridge-major-link-case-navigation": {"layout": {"visibility": "none"}}, "tunnel-construction": {"layout": {"visibility": "none"}}, "bridge-street-case": {"layout": {"visibility": "none"}}, "bridge-minor-link": {"layout": {"visibility": "none"}}, "bridge-motorway-trunk-2-navigation": {"layout": {"visibility": "none"}}, "tunnel-motorway-trunk-case": {"layout": {"visibility": "none"}}, "tunnel-major-link-case-navigation": {"layout": {"visibility": "none"}}, "bridge-major-link-2": {"layout": {"visibility": "none"}}, "tunnel-minor-link": {"layout": {"visibility": "none"}}, "tunnel-simple": {"layout": {"visibility": "none"}}, "road-street-low": {"layout": {"visibility": "none"}}, "road-label-simple": {"layout": {"visibility": "none"}}, "road-secondary-tertiary": {"layout": {"visibility": "none"}}, "bridge-motorway-trunk-case": {"layout": {"visibility": "none"}}, "road-minor-navigation": {"layout": {"visibility": "none"}}, "road-primary-case-navigation": {"layout": {"visibility": "none"}}, "tunnel-street-navigation": {"layout": {"visibility": "none"}}, "bridge-street-navigation": {"layout": {"visibility": "none"}}, "road-label-navigation": {"layout": {"visibility": "none"}}, "road-major-link": {"layout": {"visibility": "none"}}, "tunnel-oneway-arrow-white-navigation": {"layout": {"visibility": "none"}}, "bridge-major-link-2-case-navigation": {"layout": {"visibility": "none"}}, "tunnel-motorway-trunk-navigation": {"layout": {"visibility": "none"}}, "road-secondary-tertiary-case-navigation": {"layout": {"visibility": "none"}}, "bridge-motorway-trunk-navigation": {"layout": {"visibility": "none"}}, "bridge-oneway-arrow-white-navigation": {"layout": {"visibility": "none"}}, "tunnel-primary": {"layout": {"visibility": "none"}}, "tunnel-oneway-arrow-blue": {"layout": {"visibility": "none"}}, "road-minor-link-case": {"layout": {"visibility": "none"}}, "turning-feature-outline-navigation": {"layout": {"visibility": "none"}}, "road-construction": {"layout": {"visibility": "none"}}, "bridge-primary": {"layout": {"visibility": "none"}}, "bridge-oneway-arrow-blue": {"layout": {"visibility": "none"}}, "road-simple": {"layout": {"visibility": "none"}}, "road-major-link-case-navigation": {"layout": {"visibility": "none"}}, "road-minor-link": {"layout": {"visibility": "none"}}, "bridge-primary-case": {"layout": {"visibility": "none"}}}, "walking-cycling": {"road-path-bg": {"layout": {"visibility": "none"}}, "tunnel-pedestrian": {"layout": {"visibility": "none"}}, "tunnel-steps": {"layout": {"visibility": "none"}}, "bridge-path-trail": {"layout": {"visibility": "none"}}, "road-path-cycleway-piste": {"layout": {"visibility": "none"}}, "tunnel-path-trail": {"layout": {"visibility": "none"}}, "bridge-steps": {"layout": {"visibility": "none"}}, "gate-fence-hedge": {"layout": {"visibility": "none"}}, "road-pedestrian": {"layout": {"visibility": "none"}}, "bridge-path-bg": {"layout": {"visibility": "none"}}, "bridge-path": {"layout": {"visibility": "none"}}, "tunnel-path-cycleway-piste": {"layout": {"visibility": "none"}}, "tunnel-path": {"layout": {"visibility": "none"}}, "path-pedestrian-label": {"layout": {"visibility": "none"}}, "bridge-path-cycleway-piste": {"layout": {"visibility": "none"}}, "road-path-trail": {"layout": {"visibility": "none"}}, "bridge-steps-bg": {"layout": {"visibility": "none"}}, "golf-hole-label": {"layout": {"visibility": "none"}}, "road-path": {"layout": {"visibility": "none"}}, "gate-label": {"layout": {"visibility": "none"}}, "road-pedestrian-case": {"layout": {"visibility": "none"}}, "golf-hole-line": {"layout": {"visibility": "none"}}, "road-steps-bg": {"layout": {"visibility": "none"}}, "road-pedestrian-polygon-pattern": {"layout": {"visibility": "none"}}, "road-steps": {"layout": {"visibility": "none"}}, "road-pedestrian-polygon-fill": {"layout": {"visibility": "none"}}, "bridge-pedestrian-case": {"layout": {"visibility": "none"}}, "bridge-pedestrian": {"layout": {"visibility": "none"}}}, "buildings": {"building-extrusion": {"layout": {"visibility": "none"}}, "building": {"layout": {"visibility": "none"}}, "building-entrance": {"layout": {"visibility": "none"}}, "building-number-label": {"layout": {"visibility": "none"}}, "building-underground": {"layout": {"visibility": "none"}}, "block-number-label": {"layout": {"visibility": "none"}}}}, "components": {"road-network": "18.0.0", "natural-features": "18.0.0", "place-labels": "18.0.0", "admin-boundaries": "18.0.0", "point-of-interest-labels": "18.0.0", "walking-cycling": "18.0.0", "transit": "18.0.0", "land-and-water": "18.0.0", "buildings": "18.0.0"}, "propConfig": {"road-network": {"colorBase": "hsla(221, 20%, 95%, 0)", "colorRoad": "hsl(0, 0%, 100%)", "colorRoadOutline": "hsl(220, 20%, 85%)", "colorMotorwayTrunk": "hsl(40, 90%, 70%)", "colorRoadLabel": "hsl(0,0%, 0%)", "turningCircles": true, "roadMarkings": true}, "natural-features": {"colorBase": "hsla(221, 20%, 95%, 0)", "colorWater": "hsla(189, 75%, 51%, 0)", "colorPoi": "hsl(210, 20%, 58%)"}, "place-labels": {"states": true, "settlements": false, "settlementSubdivisions": false, "colorPlaceLabel": "hsl(220, 30%, 30%)", "colorBase": "hsla(221, 20%, 95%, 0)", "countries": true, "worldview": "CN", "language": "Simplified Chinese", "settlementSubdivisionsDensity": 3, "continents": false}, "admin-boundaries": {"colorBase": "hsla(221, 20%, 95%, 0)", "colorAdminBoundary": "hsl(240, 50%, 60%)", "worldview": "CN"}, "point-of-interest-labels": {"colorPoiEntertainment": "hsl(320, 70%, 75%)", "colorPoiEducation": "hsl(30, 50%, 55%)", "colorPoiFoodAndDrink": "hsl(40, 95%, 55%)", "poiIconScale": 0.8, "colorPoi": "hsl(210, 20%, 58%)", "colorPoiParkLike": "hsl(110, 70%, 40%)", "colorPoiMedical": "hsl(0, 70%, 70%)", "colorPoiCommercialServices": "hsl(260, 70%, 75%)", "poiIconBackground": "Circle", "colorPoiStoreLike": "hsl(210, 70%, 70%)", "colorBase": "hsla(221, 20%, 95%, 0)", "colorPoiSportAndLeisure": "hsl(190, 60%, 60%)"}, "walking-cycling": {"colorPoi": "hsl(210, 20%, 58%)", "colorBase": "hsla(221, 20%, 95%, 0)", "colorGreenspace": "#279211", "colorPoiParkLike": "hsl(110, 70%, 40%)", "colorRoad": "hsl(0, 0%, 100%)", "colorRoadOutline": "hsl(220, 20%, 85%)", "colorRoadLabel": "hsl(0,0%, 0%)"}, "transit": {"poiIconBackground": "Rectangle", "poiIconScale": 0.8, "colorPoi": "hsl(210, 20%, 58%)", "colorBase": "hsla(221, 20%, 95%, 0)", "colorTransit": "hsl(225, 60%, 58%)", "colorWater": "hsla(189, 75%, 51%, 0)", "colorAirport": "hsl(225, 60%, 58%)", "colorRoadOutline": "hsl(220, 20%, 85%)"}, "land-and-water": {"colorEducation": "hsl(40, 50%, 88%)", "colorGreenspace": "#279211", "colorPoi": "hsl(210, 20%, 58%)", "colorPoiParkLike": "hsl(110, 70%, 40%)", "colorAirport": "hsl(225, 60%, 58%)", "colorIndustrialArea": "hsl(230, 20%, 90%)", "colorMedical": "hsl(0, 50%, 92%)", "colorBase": "hsla(221, 20%, 95%, 0)", "colorWater": "hsla(189, 75%, 51%, 0)", "colorCommercialArea": "hsl(45, 55%, 93%)", "bathymetry": true}, "buildings": {"colorBase": "hsla(221, 20%, 95%, 0)"}}}, "mapbox:thumb": "data:image/webp;base64,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", "mapbox:autocomposite": true, "mapbox:sdk-support": {"js": "3.11.0", "android": "11.12.0", "ios": "11.12.0"}, "mapbox:origin": "streets-v12", "mapbox:groups": {"Transit, transit-labels": {"name": "Transit, transit-labels", "collapsed": true}, "Administrative boundaries, admin": {"name": "Administrative boundaries, admin", "collapsed": true}, "Land & water, built": {"name": "Land & water, built", "collapsed": false}, "Transit, bridges": {"name": "Transit, bridges", "collapsed": true}, "Buildings, building-labels": {"name": "Buildings, building-labels", "collapsed": true}, "Transit, surface": {"name": "Transit, surface", "collapsed": true}, "Land & water, land": {"name": "Land & water, land", "collapsed": false}, "Road network, bridges": {"name": "Road network, bridges", "collapsed": true}, "Road network, tunnels": {"name": "Road network, tunnels", "collapsed": true}, "Road network, road-labels": {"name": "Road network, road-labels", "collapsed": true}, "Buildings, built": {"name": "Buildings, built", "collapsed": true}, "Natural features, natural-labels": {"name": "Natural features, natural-labels", "collapsed": true}, "Road network, surface": {"name": "Road network, surface", "collapsed": true}, "Walking, cycling, etc., barriers-bridges": {"name": "Walking, cycling, etc., barriers-bridges", "collapsed": true}, "Place labels, place-labels": {"name": "Place labels, place-labels", "collapsed": true}, "Point of interest labels, poi-labels": {"name": "Point of interest labels, poi-labels", "collapsed": true}, "Walking, cycling, etc., tunnels": {"name": "Walking, cycling, etc., tunnels", "collapsed": true}, "Walking, cycling, etc., walking-cycling-labels": {"name": "Walking, cycling, etc., walking-cycling-labels", "collapsed": true}, "Walking, cycling, etc., surface": {"name": "Walking, cycling, etc., surface", "collapsed": true}, "Transit, built": {"name": "Transit, built", "collapsed": true}, "Land & water, water": {"name": "Land & water, water", "collapsed": false}, "Transit, ferry-aerialway-labels": {"name": "Transit, ferry-aerialway-labels", "collapsed": true}}}, "center": [104.15867140801129, 21.805239507601556], "zoom": 4.430208272785052, "bearing": 0, "pitch": 0, "fog": {"range": [2, 20], "color": "hsl(0, 0%, 100%)", "high-color": "hsl(210, 100%, 80%)", "space-color": ["interpolate", ["exponential", 1.2], ["zoom"], 5, "hsl(210, 40%, 30%)", 7, "hsl(210, 100%, 80%)"], "horizon-blend": ["interpolate", ["exponential", 1.2], ["zoom"], 5, 0.02, 7, 0.08], "star-intensity": ["interpolate", ["exponential", 1.2], ["zoom"], 5, 0.1, 7, 0]}, "imports": [{"id": "basemap", "url": "mapbox://styles/mapbox/standard"}], "sources": {"composite": {"url": "mapbox://mapbox.mapbox-streets-v8,mapbox.mapbox-terrain-v2,mapbox.mapbox-bathymetry-v2", "type": "vector"}}, "sprite": "mapbox://sprites/bar5xc/cmbsr6jaz011g01sd03vc9g1x/3l284do0ujn304hn0mvgapn59", "glyphs": "mapbox://fonts/mapbox/{fontstack}/{range}.pbf", "projection": {"name": "equirectangular"}, "layers": [{"id": "land", "type": "background", "metadata": {"mapbox:featureComponent": "land-and-water", "mapbox:group": "Land & water, land"}, "layout": {}, "paint": {"background-color": "rgba(0, 0, 0, 0)"}}, {"id": "landcover", "type": "fill", "metadata": {"mapbox:featureComponent": "land-and-water", "mapbox:group": "Land & water, land"}, "source": "composite", "source-layer": "landcover", "maxzoom": 12, "filter": ["match", ["get", "class"], ["scrub", "grass"], ["step", ["zoom"], true, 8, false], true], "layout": {}, "paint": {"fill-color": ["match", ["get", "class"], "wood", "hsl(160, 96%, 22%)", "scrub", "hsl(159, 34%, 52%)", "grass", "hsl(159, 34%, 52%)", "hsl(159, 34%, 52%)"], "fill-opacity": ["interpolate", ["exponential", 1.5], ["zoom"], 9, 0.3, 10, 0.4, 11, 0.3, 12, 0], "fill-antialias": false}}, {"id": "national-park", "type": "fill", "metadata": {"mapbox:featureComponent": "land-and-water", "mapbox:group": "Land & water, land"}, "source": "composite", "source-layer": "landuse_overlay", "minzoom": 5, "filter": ["==", ["get", "class"], "national_park"], "layout": {}, "paint": {"fill-color": "hsl(160, 96%, 22%)", "fill-opacity": ["interpolate", ["linear"], ["zoom"], 5, 0, 6, 0.3, 12, 0.1]}}, {"id": "landuse", "type": "fill", "metadata": {"mapbox:featureComponent": "land-and-water", "mapbox:group": "Land & water, land"}, "source": "composite", "source-layer": "landuse", "minzoom": 5, "filter": ["all", [">=", ["to-number", ["get", "sizerank"]], 0], ["match", ["get", "class"], ["agriculture", "wood", "grass", "scrub", "glacier", "pitch", "sand"], ["step", ["zoom"], false, 11, true], "residential", ["step", ["zoom"], true, 10, false], ["park", "airport"], ["step", ["zoom"], false, 8, ["case", ["==", ["get", "sizerank"], 1], true, false], 10, true], ["facility", "industrial"], ["step", ["zoom"], false, 12, true], "cemetery", ["step", ["zoom"], false, 11, true], "school", ["step", ["zoom"], false, 11, true], "hospital", ["step", ["zoom"], false, 11, true], "commercial_area", ["step", ["zoom"], false, 11, true], false], ["<=", ["-", ["to-number", ["get", "sizerank"]], ["interpolate", ["exponential", 1.5], ["zoom"], 12, 0, 18, 14]], 14]], "layout": {}, "paint": {"fill-color": ["interpolate", ["linear"], ["zoom"], 15, ["match", ["get", "class"], "wood", "hsl(160, 96%, 22%)", "scrub", "hsl(159, 34%, 52%)", "agriculture", "hsl(159, 34%, 52%)", "park", "hsl(160, 96%, 22%)", "grass", "hsl(159, 34%, 52%)", "glacier", "hsl(160, 96%, 22%)", "sand", "hsl(159, 34%, 52%)", "hsl(159, 34%, 52%)"], 16, ["match", ["get", "class"], "wood", "hsl(160, 96%, 22%)", "scrub", "hsl(159, 34%, 52%)", "agriculture", "hsl(159, 34%, 52%)", "park", "hsl(160, 96%, 22%)", "grass", "hsl(159, 34%, 52%)", "glacier", "hsl(160, 96%, 22%)", "sand", "hsl(159, 34%, 52%)", "hsl(159, 34%, 52%)"]], "fill-opacity": ["interpolate", ["linear"], ["zoom"], 8, 0.2, 10, 0.4, 12, 0.3], "fill-antialias": false}}, {"id": "hillshade", "type": "fill", "metadata": {"mapbox:featureComponent": "land-and-water", "mapbox:group": "Land & water, land"}, "source": "composite", "source-layer": "hillshade", "maxzoom": 16, "filter": ["all", ["step", ["zoom"], ["==", ["get", "class"], "shadow"], 11, true], ["match", ["get", "level"], 89, true, 78, ["step", ["zoom"], false, 5, true], 67, ["step", ["zoom"], false, 9, true], 56, ["step", ["zoom"], false, 6, true], 94, ["step", ["zoom"], false, 11, true], 90, ["step", ["zoom"], false, 12, true], false]], "layout": {}, "paint": {"fill-color": ["interpolate", ["linear"], ["zoom"], 14, ["match", ["get", "class"], "shadow", "hsl(159, 34%, 52%)", "hsl(160, 96%, 22%)"], 16, ["match", ["get", "class"], "shadow", "hsl(159, 34%, 52%)", "hsl(160, 96%, 22%)"]], "fill-opacity": ["interpolate", ["linear"], ["zoom"], 14, 1.0, 16, 1.0], "fill-outline-color": "hsl(159, 56%, 42%)", "fill-antialias": true}}, {"id": "pitch-outline", "type": "line", "metadata": {"mapbox:featureComponent": "land-and-water", "mapbox:group": "Land & water, land"}, "source": "composite", "source-layer": "landuse", "minzoom": 15, "filter": ["==", ["get", "class"], "pitch"], "layout": {}, "paint": {"line-color": "hsl(160, 96%, 22%)", "line-opacity": 0}}, {"id": "waterway-shadow", "type": "line", "metadata": {"mapbox:featureComponent": "land-and-water", "mapbox:group": "Land & water, water"}, "source": "composite", "source-layer": "waterway", "minzoom": 10, "layout": {"line-cap": ["step", ["zoom"], "butt", 11, "round"], "line-join": ["step", ["zoom"], "miter", 11, "round"]}, "paint": {"line-color": "hsla(208, 79%, 50%, 0)", "line-width": ["interpolate", ["exponential", 1.3], ["zoom"], 9, ["match", ["get", "class"], ["canal", "river"], 0.1, 0], 20, ["match", ["get", "class"], ["canal", "river"], 8, 3]], "line-translate": ["interpolate", ["exponential", 1.2], ["zoom"], 7, ["literal", [0, 0]], 16, ["literal", [-1, -1]]], "line-translate-anchor": "viewport", "line-opacity": ["interpolate", ["linear"], ["zoom"], 8, 0, 8.5, 1]}}, {"id": "water-shadow", "type": "fill", "metadata": {"mapbox:featureComponent": "land-and-water", "mapbox:group": "Land & water, water"}, "source": "composite", "source-layer": "water", "minzoom": 10, "layout": {}, "paint": {"fill-color": "hsla(208, 79%, 50%, 0)", "fill-translate": ["interpolate", ["exponential", 1.2], ["zoom"], 7, ["literal", [0, 0]], 16, ["literal", [-1, -1]]], "fill-translate-anchor": "viewport"}}, {"id": "waterway", "type": "line", "metadata": {"mapbox:featureComponent": "land-and-water", "mapbox:group": "Land & water, water"}, "source": "composite", "source-layer": "waterway", "minzoom": 8, "layout": {"line-cap": ["step", ["zoom"], "butt", 11, "round"], "line-join": ["step", ["zoom"], "miter", 11, "round"]}, "paint": {"line-color": "hsla(189, 75%, 51%, 0)", "line-width": ["interpolate", ["exponential", 1.3], ["zoom"], 9, ["match", ["get", "class"], ["canal", "river"], 0.1, 0], 20, ["match", ["get", "class"], ["canal", "river"], 8, 3]], "line-opacity": ["interpolate", ["linear"], ["zoom"], 8, 0, 8.5, 1]}}, {"id": "water", "type": "fill", "metadata": {"mapbox:featureComponent": "land-and-water", "mapbox:group": "Land & water, water"}, "source": "composite", "source-layer": "water", "layout": {}, "paint": {"fill-color": "hsla(189, 75%, 51%, 0)", "fill-opacity": 0}}, {"id": "water-depth", "type": "fill", "metadata": {"mapbox:featureComponent": "land-and-water", "mapbox:group": "Land & water, water"}, "source": "composite", "source-layer": "depth", "maxzoom": 8, "layout": {}, "paint": {"fill-antialias": false, "fill-color": ["interpolate", ["linear"], ["get", "min_depth"], 0, "hsla(189, 75%, 51%, 0)", 200, "hsla(189, 75%, 46%, 0)", 7000, "hsla(189, 75%, 38%, 0)"]}}, {"id": "land-structure-polygon", "type": "fill", "metadata": {"mapbox:featureComponent": "land-and-water", "mapbox:group": "Land & water, built"}, "source": "composite", "source-layer": "structure", "minzoom": 13, "filter": ["all", ["==", ["get", "class"], "land"], ["==", ["geometry-type"], "Polygon"]], "layout": {}, "paint": {"fill-color": "hsl(160, 96%, 22%)", "fill-opacity": 0}}, {"id": "land-structure-line", "type": "line", "metadata": {"mapbox:featureComponent": "land-and-water", "mapbox:group": "Land & water, built"}, "source": "composite", "source-layer": "structure", "minzoom": 13, "filter": ["all", ["==", ["get", "class"], "land"], ["==", ["geometry-type"], "LineString"]], "layout": {"line-cap": "square"}, "paint": {"line-width": ["interpolate", ["exponential", 1.99], ["zoom"], 14, 0.75, 20, 40], "line-color": "hsl(160, 96%, 22%)", "line-opacity": 0}}, {"id": "aeroway-polygon", "type": "fill", "metadata": {"mapbox:featureComponent": "transit", "mapbox:group": "Transit, built"}, "source": "composite", "source-layer": "aeroway", "minzoom": 11, "filter": ["all", ["match", ["get", "type"], ["runway", "taxiway", "helipad"], true, false], ["==", ["geometry-type"], "Polygon"]], "layout": {"visibility": "none"}, "paint": {"fill-color": "hsl(225, 52%, 87%)", "fill-opacity": ["interpolate", ["linear"], ["zoom"], 10, 0, 11, 1]}}, {"id": "aeroway-line", "type": "line", "metadata": {"mapbox:featureComponent": "transit", "mapbox:group": "Transit, built"}, "source": "composite", "source-layer": "aeroway", "minzoom": 9, "filter": ["==", ["geometry-type"], "LineString"], "layout": {"visibility": "none"}, "paint": {"line-color": "hsl(225, 52%, 87%)", "line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 9, ["match", ["get", "type"], "runway", 1, 0.5], 18, ["match", ["get", "type"], "runway", 80, 20]], "line-opacity": ["interpolate", ["linear"], ["zoom"], 10, 0, 11, 1]}}, {"id": "building", "type": "fill", "metadata": {"mapbox:featureComponent": "buildings", "mapbox:group": "Buildings, built"}, "source": "composite", "source-layer": "building", "minzoom": 15, "filter": ["all", ["!=", ["get", "type"], "building:part"], ["==", ["get", "underground"], "false"]], "layout": {"visibility": "none"}, "paint": {"fill-color": "hsla(221, 15%, 85%, 0)", "fill-opacity": ["interpolate", ["linear"], ["zoom"], 15, 0, 16, 1], "fill-outline-color": "hsla(221, 10%, 72%, 0)"}}, {"id": "tunnel-path", "type": "line", "metadata": {"mapbox:featureComponent": "walking-cycling", "mapbox:group": "Walking, cycling, etc., tunnels"}, "source": "composite", "source-layer": "road", "minzoom": 15, "filter": ["all", ["==", ["get", "structure"], "tunnel"], ["==", ["get", "class"], "path"], ["!=", ["get", "type"], "steps"], ["==", ["geometry-type"], "LineString"]], "layout": {"visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 15, 1, 18, 4], "line-color": "hsla(221, 32%, 95%, 0)", "line-dasharray": ["step", ["zoom"], ["literal", [1, 0]], 15, ["literal", [1.75, 1]], 16, ["literal", [1, 0.75]], 17, ["literal", [1, 0.5]]]}}, {"id": "tunnel-steps", "type": "line", "metadata": {"mapbox:featureComponent": "walking-cycling", "mapbox:group": "Walking, cycling, etc., tunnels"}, "source": "composite", "source-layer": "road", "minzoom": 15, "filter": ["all", ["==", ["get", "structure"], "tunnel"], ["==", ["get", "type"], "steps"], ["==", ["geometry-type"], "LineString"]], "layout": {"visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 15, 1, 16, 1.6, 18, 6], "line-color": "hsla(221, 32%, 95%, 0)", "line-dasharray": ["step", ["zoom"], ["literal", [1, 0]], 15, ["literal", [1.75, 1]], 16, ["literal", [1, 0.75]], 17, ["literal", [0.3, 0.3]]]}}, {"id": "tunnel-pedestrian", "type": "line", "metadata": {"mapbox:featureComponent": "walking-cycling", "mapbox:group": "Walking, cycling, etc., tunnels"}, "source": "composite", "source-layer": "road", "minzoom": 15, "filter": ["all", ["==", ["get", "structure"], "tunnel"], ["==", ["get", "class"], "pedestrian"], ["==", ["geometry-type"], "LineString"]], "layout": {"visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 14, 0.5, 18, 12], "line-color": "hsl(0, 0%, 100%)", "line-dasharray": ["step", ["zoom"], ["literal", [2, 0.3]], 15, ["literal", [1, 0.3]], 16, ["literal", [1, 0.3]], 17, ["literal", [1, 0.25]]]}}, {"id": "tunnel-minor-case", "type": "line", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, tunnels"}, "source": "composite", "source-layer": "road", "minzoom": 13, "filter": ["all", ["==", ["get", "structure"], "tunnel"], ["match", ["get", "class"], ["track"], true, "service", ["step", ["zoom"], false, 14, true], false], ["==", ["geometry-type"], "LineString"]], "layout": {"visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 14, 0.8, 22, 2], "line-color": "hsl(220, 13%, 72%)", "line-gap-width": ["interpolate", ["exponential", 1.5], ["zoom"], 14, 1, 18, 10, 22, 100], "line-dasharray": [3, 3]}}, {"id": "tunnel-street-case", "type": "line", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, tunnels"}, "source": "composite", "source-layer": "road", "minzoom": 14, "filter": ["all", ["==", ["get", "structure"], "tunnel"], ["match", ["get", "class"], ["street", "street_limited"], true, false], ["==", ["geometry-type"], "LineString"]], "layout": {"visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 14, 0.8, 22, 2], "line-color": "hsl(220, 13%, 72%)", "line-gap-width": ["interpolate", ["exponential", 1.5], ["zoom"], 12, 0.5, 18, 20, 22, 200], "line-opacity": ["step", ["zoom"], 0, 14, 1], "line-dasharray": [3, 3]}}, {"id": "tunnel-minor-link-case", "type": "line", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, tunnels"}, "source": "composite", "source-layer": "road", "minzoom": 13, "filter": ["all", ["match", ["get", "class"], ["primary_link", "secondary_link", "tertiary_link"], true, false], ["==", ["get", "structure"], "tunnel"], ["==", ["geometry-type"], "LineString"]], "layout": {"line-cap": ["step", ["zoom"], "butt", 14, "round"], "line-join": ["step", ["zoom"], "miter", 14, "round"], "visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 14, 0.8, 22, 2], "line-color": "hsl(220, 20%, 85%)", "line-gap-width": ["interpolate", ["exponential", 1.5], ["zoom"], 12, 0.4, 18, 18, 22, 180], "line-opacity": ["step", ["zoom"], 0, 11, 1]}}, {"id": "tunnel-secondary-tertiary-case", "type": "line", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, tunnels"}, "source": "composite", "source-layer": "road", "minzoom": 11, "filter": ["all", ["==", ["get", "structure"], "tunnel"], ["match", ["get", "class"], ["secondary", "tertiary"], true, false], ["==", ["geometry-type"], "LineString"]], "layout": {"visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 14, 1, 22, 2], "line-color": "hsl(220, 13%, 72%)", "line-gap-width": ["interpolate", ["exponential", 1.5], ["zoom"], 3, 0, 18, 26, 22, 260], "line-dasharray": [3, 3]}}, {"id": "tunnel-primary-case", "type": "line", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, tunnels"}, "source": "composite", "source-layer": "road", "minzoom": 10, "filter": ["all", ["==", ["get", "structure"], "tunnel"], ["==", ["get", "class"], "primary"], ["==", ["geometry-type"], "LineString"]], "layout": {"visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 14, 1, 22, 2], "line-color": "hsl(220, 13%, 72%)", "line-gap-width": ["interpolate", ["exponential", 1.5], ["zoom"], 3, 0.8, 18, 28, 22, 280], "line-dasharray": [3, 3]}}, {"id": "tunnel-major-link-case", "type": "line", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, tunnels"}, "source": "composite", "source-layer": "road", "minzoom": 12, "filter": ["all", ["==", ["get", "structure"], "tunnel"], ["match", ["get", "class"], ["motorway_link", "trunk_link"], true, false], ["==", ["geometry-type"], "LineString"]], "layout": {"visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 14, 0.8, 22, 2], "line-color": "hsl(0, 0%, 100%)", "line-gap-width": ["interpolate", ["exponential", 1.5], ["zoom"], 12, 0.8, 18, 20, 22, 200], "line-dasharray": [3, 3]}}, {"id": "tunnel-motorway-trunk-case", "type": "line", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, tunnels"}, "source": "composite", "source-layer": "road", "minzoom": 13, "filter": ["all", ["==", ["get", "structure"], "tunnel"], ["match", ["get", "class"], ["motorway", "trunk"], true, false], ["==", ["geometry-type"], "LineString"]], "layout": {"visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 14, 1, 22, 2], "line-color": "hsl(220, 20%, 97%)", "line-gap-width": ["interpolate", ["exponential", 1.5], ["zoom"], 3, 0.8, 18, 30, 22, 300], "line-dasharray": [3, 3]}}, {"id": "tunnel-construction", "type": "line", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, tunnels"}, "source": "composite", "source-layer": "road", "minzoom": 14, "filter": ["all", ["==", ["get", "structure"], "tunnel"], ["==", ["get", "class"], "construction"], ["==", ["geometry-type"], "LineString"]], "layout": {"visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 14, 2, 18, 20, 22, 200], "line-color": "hsl(220, 20%, 85%)", "line-dasharray": ["step", ["zoom"], ["literal", [0.4, 0.8]], 15, ["literal", [0.3, 0.6]], 16, ["literal", [0.2, 0.3]], 17, ["literal", [0.2, 0.25]], 18, ["literal", [0.15, 0.15]]]}}, {"id": "tunnel-minor", "type": "line", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, tunnels"}, "source": "composite", "source-layer": "road", "minzoom": 13, "filter": ["all", ["==", ["get", "structure"], "tunnel"], ["match", ["get", "class"], ["track"], true, "service", ["step", ["zoom"], false, 14, true], false], ["==", ["geometry-type"], "LineString"]], "layout": {"visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 14, 1, 18, 10, 22, 100], "line-color": ["match", ["get", "class"], "street_limited", "hsla(221, 22%, 94%, 0)", "hsl(0, 0%, 100%)"]}}, {"id": "tunnel-minor-link", "type": "line", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, tunnels"}, "source": "composite", "source-layer": "road", "minzoom": 13, "filter": ["all", ["match", ["get", "class"], ["primary_link", "secondary_link", "tertiary_link"], true, false], ["==", ["get", "structure"], "tunnel"], ["==", ["geometry-type"], "LineString"]], "layout": {"line-cap": ["step", ["zoom"], "butt", 13, "round"], "line-join": ["step", ["zoom"], "miter", 13, "round"], "visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 12, 0.4, 18, 18, 22, 180], "line-color": "hsl(0, 0%, 100%)"}}, {"id": "tunnel-major-link", "type": "line", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, tunnels"}, "source": "composite", "source-layer": "road", "minzoom": 12, "filter": ["all", ["==", ["get", "structure"], "tunnel"], ["match", ["get", "class"], ["motorway_link", "trunk_link"], true, false], ["==", ["geometry-type"], "LineString"]], "layout": {"visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 12, 0.8, 18, 20, 22, 200], "line-color": ["match", ["get", "class"], "motorway_link", "hsl(30, 100%, 80%)", "hsl(50, 78%, 80%)"]}}, {"id": "tunnel-street", "type": "line", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, tunnels"}, "source": "composite", "source-layer": "road", "minzoom": 13, "filter": ["all", ["==", ["get", "structure"], "tunnel"], ["match", ["get", "class"], ["street", "street_limited"], true, false], ["==", ["geometry-type"], "LineString"]], "layout": {"visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 12, 0.5, 18, 20, 22, 200], "line-color": ["match", ["get", "class"], "street_limited", "hsla(221, 22%, 94%, 0)", "hsl(0, 0%, 100%)"], "line-opacity": ["step", ["zoom"], 0, 14, 1]}}, {"id": "tunnel-street-low", "type": "line", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, tunnels"}, "source": "composite", "source-layer": "road", "minzoom": 13, "maxzoom": 14, "filter": ["all", ["==", ["get", "structure"], "tunnel"], ["match", ["get", "class"], ["street", "street_limited"], true, false], ["==", ["geometry-type"], "LineString"]], "layout": {"line-cap": ["step", ["zoom"], "butt", 14, "round"], "line-join": ["step", ["zoom"], "miter", 14, "round"], "visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 12, 0.5, 18, 20, 22, 200], "line-color": "hsl(0, 0%, 100%)"}}, {"id": "tunnel-secondary-tertiary", "type": "line", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, tunnels"}, "source": "composite", "source-layer": "road", "minzoom": 13, "filter": ["all", ["==", ["get", "structure"], "tunnel"], ["match", ["get", "class"], ["secondary", "tertiary"], true, false], ["==", ["geometry-type"], "LineString"]], "layout": {"visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 3, 0, 18, 26, 22, 260], "line-color": "hsl(0, 0%, 100%)"}}, {"id": "tunnel-primary", "type": "line", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, tunnels"}, "source": "composite", "source-layer": "road", "minzoom": 13, "filter": ["all", ["==", ["get", "structure"], "tunnel"], ["==", ["get", "class"], "primary"], ["==", ["geometry-type"], "LineString"]], "layout": {"visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 3, 0.8, 18, 28, 22, 280], "line-color": "hsl(0, 0%, 100%)"}}, {"id": "tunnel-motorway-trunk", "type": "line", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, tunnels"}, "source": "composite", "source-layer": "road", "minzoom": 13, "filter": ["all", ["==", ["get", "structure"], "tunnel"], ["match", ["get", "class"], ["motorway", "trunk"], true, false], ["==", ["geometry-type"], "LineString"]], "layout": {"visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 3, 0.8, 18, 30, 22, 300], "line-color": ["match", ["get", "class"], "motorway", "hsl(30, 100%, 80%)", "hsl(50, 78%, 80%)"]}}, {"id": "tunnel-oneway-arrow-blue", "type": "symbol", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, tunnels"}, "source": "composite", "source-layer": "road", "minzoom": 16, "filter": ["all", ["==", ["get", "structure"], "tunnel"], ["==", ["get", "oneway"], "true"], ["step", ["zoom"], ["match", ["get", "class"], ["primary", "secondary", "street", "street_limited", "tertiary"], true, false], 16, ["match", ["get", "class"], ["primary", "secondary", "tertiary", "street", "street_limited", "primary_link", "secondary_link", "tertiary_link", "service", "track"], true, false]], ["step", ["pitch"], true, 50, ["<", ["distance-from-center"], 1], 60, ["<", ["distance-from-center"], 1.5], 70, ["<", ["distance-from-center"], 2]]], "layout": {"symbol-placement": "line", "icon-image": ["step", ["zoom"], "oneway-small", 18, "oneway-large"], "symbol-spacing": 200, "icon-rotation-alignment": "map", "icon-allow-overlap": true, "icon-ignore-placement": true, "visibility": "none"}, "paint": {}}, {"id": "tunnel-oneway-arrow-white", "type": "symbol", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, tunnels"}, "source": "composite", "source-layer": "road", "minzoom": 16, "filter": ["all", ["==", ["get", "structure"], "tunnel"], ["match", ["get", "class"], ["motorway", "motorway_link", "trunk", "trunk_link"], true, false], ["==", ["get", "oneway"], "true"], ["step", ["pitch"], true, 50, ["<", ["distance-from-center"], 1], 60, ["<", ["distance-from-center"], 1.5], 70, ["<", ["distance-from-center"], 2]]], "layout": {"symbol-placement": "line", "icon-image": ["step", ["zoom"], "oneway-white-small", 18, "oneway-white-large"], "symbol-spacing": 200, "icon-rotation-alignment": "map", "icon-allow-overlap": true, "icon-ignore-placement": true, "visibility": "none"}, "paint": {}}, {"id": "road-pedestrian-polygon-fill", "type": "fill", "metadata": {"mapbox:featureComponent": "walking-cycling", "mapbox:group": "Walking, cycling, etc., surface"}, "source": "composite", "source-layer": "road", "minzoom": 14, "filter": ["all", ["match", ["get", "class"], ["path", "pedestrian"], true, false], ["match", ["get", "structure"], ["none", "ford"], true, false], ["case", ["has", "layer"], [">=", ["get", "layer"], 0], true], ["==", ["geometry-type"], "Polygon"]], "layout": {"visibility": "none"}, "paint": {"fill-color": "hsla(221, 20%, 94%, 0)"}}, {"id": "road-pedestrian-polygon-pattern", "type": "fill", "metadata": {"mapbox:featureComponent": "walking-cycling", "mapbox:group": "Walking, cycling, etc., surface"}, "source": "composite", "source-layer": "road", "minzoom": 16, "filter": ["all", ["match", ["get", "class"], ["path", "pedestrian"], true, false], ["match", ["get", "structure"], ["none", "ford"], true, false], ["case", ["has", "layer"], [">=", ["get", "layer"], 0], true], ["==", ["geometry-type"], "Polygon"]], "layout": {"visibility": "none"}, "paint": {"fill-pattern": "pedestrian-polygon", "fill-opacity": ["interpolate", ["linear"], ["zoom"], 16, 0, 17, 1]}}, {"id": "road-path-bg", "type": "line", "metadata": {"mapbox:featureComponent": "walking-cycling", "mapbox:group": "Walking, cycling, etc., surface"}, "source": "composite", "source-layer": "road", "minzoom": 12, "filter": ["all", ["==", ["get", "class"], "path"], ["step", ["zoom"], ["!", ["match", ["get", "type"], ["steps", "sidewalk", "crossing"], true, false]], 16, ["!=", ["get", "type"], "steps"]], ["match", ["get", "structure"], ["none", "ford"], true, false], ["==", ["geometry-type"], "LineString"]], "layout": {"line-join": ["step", ["zoom"], "miter", 14, "round"], "visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 15, 2, 18, 7], "line-color": "hsl(220, 11%, 79%)"}}, {"id": "road-steps-bg", "type": "line", "metadata": {"mapbox:featureComponent": "walking-cycling", "mapbox:group": "Walking, cycling, etc., surface"}, "source": "composite", "source-layer": "road", "minzoom": 14, "filter": ["all", ["==", ["get", "type"], "steps"], ["match", ["get", "structure"], ["none", "ford"], true, false], ["==", ["geometry-type"], "LineString"]], "layout": {"line-join": "round", "visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 15, 2, 17, 4.6, 18, 7], "line-color": "hsl(220, 11%, 79%)", "line-opacity": 0.75}}, {"id": "road-pedestrian-case", "type": "line", "metadata": {"mapbox:featureComponent": "walking-cycling", "mapbox:group": "Walking, cycling, etc., surface"}, "source": "composite", "source-layer": "road", "minzoom": 14, "filter": ["all", ["==", ["get", "class"], "pedestrian"], ["match", ["get", "structure"], ["none", "ford"], true, false], ["case", ["has", "layer"], [">=", ["get", "layer"], 0], true], ["==", ["geometry-type"], "LineString"]], "layout": {"line-join": ["step", ["zoom"], "miter", 14, "round"], "visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 14, 2, 18, 14.5], "line-color": "hsl(220, 20%, 85%)"}}, {"id": "road-path", "type": "line", "metadata": {"mapbox:featureComponent": "walking-cycling", "mapbox:group": "Walking, cycling, etc., surface"}, "source": "composite", "source-layer": "road", "minzoom": 12, "filter": ["all", ["==", ["get", "class"], "path"], ["step", ["zoom"], ["!", ["match", ["get", "type"], ["steps", "sidewalk", "crossing"], true, false]], 16, ["!=", ["get", "type"], "steps"]], ["match", ["get", "structure"], ["none", "ford"], true, false], ["==", ["geometry-type"], "LineString"]], "layout": {"line-join": ["step", ["zoom"], "miter", 14, "round"], "visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 13, 0.5, 14, 1, 15, 1, 18, 4], "line-color": "hsl(0, 0%, 100%)", "line-dasharray": ["step", ["zoom"], ["literal", [4, 0.3]], 15, ["literal", [1.75, 0.3]], 16, ["literal", [1, 0.3]], 17, ["literal", [1, 0.25]]]}}, {"id": "road-steps", "type": "line", "metadata": {"mapbox:featureComponent": "walking-cycling", "mapbox:group": "Walking, cycling, etc., surface"}, "source": "composite", "source-layer": "road", "minzoom": 14, "filter": ["all", ["==", ["get", "type"], "steps"], ["match", ["get", "structure"], ["none", "ford"], true, false], ["==", ["geometry-type"], "LineString"]], "layout": {"line-join": "round", "visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 15, 1, 16, 1.6, 18, 6], "line-color": "hsl(0, 0%, 100%)", "line-dasharray": ["step", ["zoom"], ["literal", [1, 0]], 15, ["literal", [1.75, 1]], 16, ["literal", [1, 0.75]], 17, ["literal", [0.3, 0.3]]]}}, {"id": "road-pedestrian", "type": "line", "metadata": {"mapbox:featureComponent": "walking-cycling", "mapbox:group": "Walking, cycling, etc., surface"}, "source": "composite", "source-layer": "road", "minzoom": 12, "filter": ["all", ["==", ["get", "class"], "pedestrian"], ["match", ["get", "structure"], ["none", "ford"], true, false], ["case", ["has", "layer"], [">=", ["get", "layer"], 0], true], ["==", ["geometry-type"], "LineString"]], "layout": {"line-join": ["step", ["zoom"], "miter", 14, "round"], "visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 14, 0.5, 18, 12], "line-color": "hsl(0, 0%, 100%)", "line-dasharray": ["step", ["zoom"], ["literal", [2, 0.3]], 15, ["literal", [1, 0.3]], 16, ["literal", [1, 0.3]], 17, ["literal", [1, 0.25]]]}}, {"id": "golf-hole-line", "type": "line", "metadata": {"mapbox:featureComponent": "walking-cycling", "mapbox:group": "Walking, cycling, etc., surface"}, "source": "composite", "source-layer": "road", "minzoom": 16, "filter": ["==", ["get", "class"], "golf"], "layout": {"visibility": "none"}, "paint": {"line-color": "rgb(160, 207, 151)"}}, {"id": "road-polygon", "type": "fill", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, surface"}, "source": "composite", "source-layer": "road", "minzoom": 12, "filter": ["all", ["match", ["get", "class"], ["primary", "secondary", "tertiary", "primary_link", "secondary_link", "tertiary_link", "trunk", "trunk_link", "street", "street_limited", "track", "service"], true, false], ["match", ["get", "structure"], ["none", "ford"], true, false], ["==", ["geometry-type"], "Polygon"]], "layout": {"visibility": "none"}, "paint": {"fill-color": "hsl(0, 0%, 100%)", "fill-outline-color": "hsl(220, 20%, 85%)"}}, {"id": "turning-feature-outline", "type": "circle", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, surface"}, "source": "composite", "source-layer": "road", "minzoom": 15, "filter": ["all", ["match", ["get", "class"], ["turning_circle", "turning_loop"], true, false], ["==", ["geometry-type"], "Point"]], "layout": {"visibility": "none"}, "paint": {"circle-radius": ["interpolate", ["exponential", 1.5], ["zoom"], 15, 4.5, 16, 8, 18, 20, 22, 200], "circle-color": "hsl(0, 0%, 100%)", "circle-stroke-width": ["interpolate", ["linear"], ["zoom"], 15, 0.8, 16, 1.2, 18, 2], "circle-stroke-color": "hsl(220, 20%, 85%)", "circle-pitch-alignment": "map"}}, {"id": "road-minor-case", "type": "line", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, surface"}, "source": "composite", "source-layer": "road", "minzoom": 13, "filter": ["all", ["match", ["get", "class"], ["track"], true, "service", ["step", ["zoom"], false, 14, true], false], ["match", ["get", "structure"], ["none", "ford"], true, false], ["==", ["geometry-type"], "LineString"]], "layout": {"line-cap": ["step", ["zoom"], "butt", 14, "round"], "line-join": ["step", ["zoom"], "miter", 14, "round"], "visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 14, 0.8, 22, 2], "line-color": "hsl(220, 20%, 85%)", "line-gap-width": ["interpolate", ["exponential", 1.5], ["zoom"], 14, 1, 18, 10, 22, 100]}}, {"id": "road-street-case", "type": "line", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, surface"}, "source": "composite", "source-layer": "road", "minzoom": 14, "filter": ["all", ["match", ["get", "class"], ["street", "street_limited"], true, false], ["match", ["get", "structure"], ["none", "ford"], true, false], ["==", ["geometry-type"], "LineString"]], "layout": {"line-cap": ["step", ["zoom"], "butt", 14, "round"], "line-join": ["step", ["zoom"], "miter", 14, "round"], "visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 14, 0.8, 22, 2], "line-color": "hsl(220, 20%, 85%)", "line-gap-width": ["interpolate", ["exponential", 1.5], ["zoom"], 12, 0.5, 18, 20, 22, 200], "line-opacity": ["step", ["zoom"], 0, 14, 1]}}, {"id": "road-minor-link-case", "type": "line", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, surface"}, "source": "composite", "source-layer": "road", "minzoom": 13, "filter": ["all", ["match", ["get", "class"], ["primary_link", "secondary_link", "tertiary_link"], true, false], ["match", ["get", "structure"], ["none", "ford"], true, false], ["==", ["geometry-type"], "LineString"]], "layout": {"line-cap": ["step", ["zoom"], "butt", 14, "round"], "line-join": ["step", ["zoom"], "miter", 14, "round"], "visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 14, 0.8, 22, 2], "line-color": "hsl(220, 20%, 85%)", "line-gap-width": ["interpolate", ["exponential", 1.5], ["zoom"], 12, 0.4, 18, 18, 22, 180], "line-opacity": ["step", ["zoom"], 0, 11, 1]}}, {"id": "road-secondary-tertiary-case", "type": "line", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, surface"}, "source": "composite", "source-layer": "road", "minzoom": 11, "filter": ["all", ["match", ["get", "class"], ["secondary", "tertiary"], true, false], ["match", ["get", "structure"], ["none", "ford"], true, false], ["==", ["geometry-type"], "LineString"]], "layout": {"line-cap": ["step", ["zoom"], "butt", 14, "round"], "line-join": ["step", ["zoom"], "miter", 14, "round"], "visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 14, 0.8, 22, 2], "line-color": "hsl(220, 20%, 85%)", "line-gap-width": ["interpolate", ["exponential", 1.5], ["zoom"], 3, 0, 18, 26, 22, 260]}}, {"id": "road-primary-case", "type": "line", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, surface"}, "source": "composite", "source-layer": "road", "minzoom": 10, "filter": ["all", ["==", ["get", "class"], "primary"], ["match", ["get", "structure"], ["none", "ford"], true, false], ["==", ["geometry-type"], "LineString"]], "layout": {"line-cap": ["step", ["zoom"], "butt", 14, "round"], "line-join": ["step", ["zoom"], "miter", 14, "round"], "visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 14, 1, 22, 2], "line-color": "hsl(220, 20%, 85%)", "line-gap-width": ["interpolate", ["exponential", 1.5], ["zoom"], 3, 0.8, 18, 28, 22, 280]}}, {"id": "road-major-link-case", "type": "line", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, surface"}, "source": "composite", "source-layer": "road", "minzoom": 12, "filter": ["all", ["match", ["get", "class"], ["motorway_link", "trunk_link"], true, false], ["match", ["get", "structure"], ["none", "ford"], true, false], ["==", ["geometry-type"], "LineString"]], "layout": {"line-cap": ["step", ["zoom"], "butt", 14, "round"], "line-join": ["step", ["zoom"], "miter", 14, "round"], "visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 14, 0.8, 22, 2], "line-color": "hsl(220, 20%, 97%)", "line-gap-width": ["interpolate", ["exponential", 1.5], ["zoom"], 12, 0.8, 18, 20, 22, 200], "line-opacity": ["step", ["zoom"], 0, 11, 1]}}, {"id": "road-motorway-trunk-case", "type": "line", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, surface"}, "source": "composite", "source-layer": "road", "minzoom": 3, "filter": ["all", ["step", ["zoom"], ["match", ["get", "class"], ["motorway", "trunk"], true, false], 5, ["all", ["match", ["get", "class"], ["motorway", "trunk"], true, false], ["match", ["get", "structure"], ["none", "ford"], true, false]]], ["==", ["geometry-type"], "LineString"]], "layout": {"line-cap": ["step", ["zoom"], "butt", 14, "round"], "line-join": ["step", ["zoom"], "miter", 14, "round"], "visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 14, 1, 22, 2], "line-color": "hsl(220, 20%, 97%)", "line-gap-width": ["interpolate", ["exponential", 1.5], ["zoom"], 3, 0.8, 18, 30, 22, 300], "line-opacity": ["interpolate", ["linear"], ["zoom"], 3, 0, 3.5, 1]}}, {"id": "turning-feature", "type": "circle", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, surface"}, "source": "composite", "source-layer": "road", "minzoom": 15, "filter": ["all", ["match", ["get", "class"], ["turning_circle", "turning_loop"], true, false], ["==", ["geometry-type"], "Point"]], "layout": {"visibility": "none"}, "paint": {"circle-radius": ["interpolate", ["exponential", 1.5], ["zoom"], 15, 4.5, 16, 8, 18, 20, 22, 200], "circle-color": "hsl(0, 0%, 100%)", "circle-pitch-alignment": "map"}}, {"id": "road-construction", "type": "line", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, surface"}, "source": "composite", "source-layer": "road", "minzoom": 14, "filter": ["all", ["==", ["get", "class"], "construction"], ["match", ["get", "structure"], ["none", "ford"], true, false], ["==", ["geometry-type"], "LineString"]], "layout": {"visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 14, 2, 18, 20, 22, 200], "line-color": "hsl(0, 0%, 100%)", "line-dasharray": ["step", ["zoom"], ["literal", [0.4, 0.8]], 15, ["literal", [0.3, 0.6]], 16, ["literal", [0.2, 0.3]], 17, ["literal", [0.2, 0.25]], 18, ["literal", [0.15, 0.15]]]}}, {"id": "road-minor", "type": "line", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, surface"}, "source": "composite", "source-layer": "road", "minzoom": 13, "filter": ["all", ["match", ["get", "class"], ["track"], true, "service", ["step", ["zoom"], false, 14, true], false], ["match", ["get", "structure"], ["none", "ford"], true, false], ["==", ["geometry-type"], "LineString"]], "layout": {"line-cap": ["step", ["zoom"], "butt", 14, "round"], "line-join": ["step", ["zoom"], "miter", 14, "round"], "visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 14, 1, 18, 10, 22, 100], "line-color": "hsl(0, 0%, 100%)"}}, {"id": "road-minor-link", "type": "line", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, surface"}, "source": "composite", "source-layer": "road", "minzoom": 12, "filter": ["all", ["match", ["get", "class"], ["primary_link", "secondary_link", "tertiary_link"], true, false], ["match", ["get", "structure"], ["none", "ford"], true, false], ["==", ["geometry-type"], "LineString"]], "layout": {"line-cap": ["step", ["zoom"], "butt", 13, "round"], "line-join": ["step", ["zoom"], "miter", 13, "round"], "visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 12, 0.4, 18, 18, 22, 180], "line-color": "hsl(0, 0%, 100%)"}}, {"id": "road-major-link", "type": "line", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, surface"}, "source": "composite", "source-layer": "road", "minzoom": 12, "filter": ["all", ["match", ["get", "class"], ["motorway_link", "trunk_link"], true, false], ["match", ["get", "structure"], ["none", "ford"], true, false], ["==", ["geometry-type"], "LineString"]], "layout": {"line-cap": ["step", ["zoom"], "butt", 13, "round"], "line-join": ["step", ["zoom"], "miter", 13, "round"], "visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 12, 0.8, 18, 20, 22, 200], "line-color": ["match", ["get", "class"], "motorway_link", "hsl(30, 100%, 70%)", "hsl(50, 89%, 70%)"]}}, {"id": "road-street", "type": "line", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, surface"}, "source": "composite", "source-layer": "road", "minzoom": 13, "filter": ["all", ["match", ["get", "class"], ["street", "street_limited"], true, false], ["match", ["get", "structure"], ["none", "ford"], true, false], ["==", ["geometry-type"], "LineString"]], "layout": {"line-cap": ["step", ["zoom"], "butt", 14, "round"], "line-join": ["step", ["zoom"], "miter", 14, "round"], "visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 12, 0.5, 18, 20, 22, 200], "line-color": ["match", ["get", "class"], "street_limited", "hsla(221, 22%, 94%, 0)", "hsl(0, 0%, 100%)"], "line-opacity": ["step", ["zoom"], 0, 14, 1]}}, {"id": "road-street-low", "type": "line", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, surface"}, "source": "composite", "source-layer": "road", "minzoom": 11, "maxzoom": 14, "filter": ["all", ["match", ["get", "class"], ["street", "street_limited"], true, false], ["match", ["get", "structure"], ["none", "ford"], true, false], ["==", ["geometry-type"], "LineString"]], "layout": {"line-cap": ["step", ["zoom"], "butt", 14, "round"], "line-join": ["step", ["zoom"], "miter", 14, "round"], "visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 12, 0.5, 18, 20, 22, 200], "line-color": "hsl(0, 0%, 100%)"}}, {"id": "road-secondary-tertiary", "type": "line", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, surface"}, "source": "composite", "source-layer": "road", "minzoom": 9, "filter": ["all", ["match", ["get", "class"], ["secondary", "tertiary"], true, false], ["match", ["get", "structure"], ["none", "ford"], true, false], ["==", ["geometry-type"], "LineString"]], "layout": {"line-cap": ["step", ["zoom"], "butt", 14, "round"], "line-join": ["step", ["zoom"], "miter", 14, "round"], "visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 3, 0, 18, 26, 22, 260], "line-color": "hsl(0, 0%, 100%)"}}, {"id": "road-primary", "type": "line", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, surface"}, "source": "composite", "source-layer": "road", "minzoom": 6, "filter": ["all", ["==", ["get", "class"], "primary"], ["match", ["get", "structure"], ["none", "ford"], true, false], ["==", ["geometry-type"], "LineString"]], "layout": {"line-cap": ["step", ["zoom"], "butt", 14, "round"], "line-join": ["step", ["zoom"], "miter", 14, "round"], "visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 3, 0.8, 18, 28, 22, 280], "line-color": "hsl(0, 0%, 100%)"}}, {"id": "road-motorway-trunk", "type": "line", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, surface"}, "source": "composite", "source-layer": "road", "minzoom": 3, "filter": ["all", ["step", ["zoom"], ["match", ["get", "class"], ["motorway", "trunk"], true, false], 5, ["all", ["match", ["get", "class"], ["motorway", "trunk"], true, false], ["match", ["get", "structure"], ["none", "ford"], true, false]]], ["==", ["geometry-type"], "LineString"]], "layout": {"line-cap": ["step", ["zoom"], "butt", 13, "round"], "line-join": ["step", ["zoom"], "miter", 13, "round"], "visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 3, 0.8, 18, 30, 22, 300], "line-color": ["step", ["zoom"], ["match", ["get", "class"], "motorway", "hsl(30, 88%, 64%)", "trunk", "hsl(50, 81%, 54%)", "hsla(221, 18%, 100%, 0)"], 9, ["match", ["get", "class"], "motorway", "hsl(30, 100%, 70%)", "hsl(50, 89%, 70%)"]], "line-opacity": ["interpolate", ["linear"], ["zoom"], 3, 0, 3.5, 1]}}, {"id": "level-crossing", "type": "symbol", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, surface"}, "source": "composite", "source-layer": "road", "minzoom": 16, "filter": ["all", ["==", ["get", "class"], "level_crossing"], ["step", ["pitch"], true, 50, ["<", ["distance-from-center"], 1], 60, ["<", ["distance-from-center"], 1.5], 70, ["<", ["distance-from-center"], 2]]], "layout": {"icon-image": "level-crossing", "icon-rotation-alignment": "map", "icon-allow-overlap": true, "icon-ignore-placement": true, "visibility": "none"}, "paint": {}}, {"id": "road-oneway-arrow-blue", "type": "symbol", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, surface"}, "source": "composite", "source-layer": "road", "minzoom": 16, "filter": ["all", ["==", ["get", "oneway"], "true"], ["step", ["zoom"], ["match", ["get", "class"], ["primary", "secondary", "tertiary", "street", "street_limited"], true, false], 16, ["match", ["get", "class"], ["primary", "secondary", "tertiary", "street", "street_limited", "primary_link", "secondary_link", "tertiary_link", "service", "track"], true, false]], ["match", ["get", "structure"], ["none", "ford"], true, false], ["step", ["pitch"], true, 50, ["<", ["distance-from-center"], 1], 60, ["<", ["distance-from-center"], 1.5], 70, ["<", ["distance-from-center"], 2]]], "layout": {"symbol-placement": "line", "icon-image": ["step", ["zoom"], "oneway-small", 18, "oneway-large"], "symbol-spacing": 200, "icon-rotation-alignment": "map", "icon-allow-overlap": true, "icon-ignore-placement": true, "visibility": "none"}, "paint": {}}, {"id": "road-oneway-arrow-white", "type": "symbol", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, surface"}, "source": "composite", "source-layer": "road", "minzoom": 16, "filter": ["all", ["==", ["get", "oneway"], "true"], ["match", ["get", "class"], ["motorway", "trunk", "motorway_link", "trunk_link"], true, false], ["match", ["get", "structure"], ["none", "ford"], true, false], ["step", ["pitch"], true, 50, ["<", ["distance-from-center"], 1], 60, ["<", ["distance-from-center"], 1.5], 70, ["<", ["distance-from-center"], 2]]], "layout": {"symbol-placement": "line", "icon-image": ["step", ["zoom"], "oneway-white-small", 18, "oneway-white-large"], "symbol-spacing": 200, "icon-rotation-alignment": "map", "icon-allow-overlap": true, "icon-ignore-placement": true, "visibility": "none"}, "paint": {}}, {"id": "crosswalks", "type": "symbol", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, surface"}, "source": "composite", "source-layer": "structure", "minzoom": 17, "filter": ["all", ["==", ["get", "type"], "crosswalk"], ["==", ["geometry-type"], "Point"], ["step", ["pitch"], true, 50, ["<", ["distance-from-center"], 1], 60, ["<", ["distance-from-center"], 1.5], 70, ["<", ["distance-from-center"], 2]]], "layout": {"icon-size": ["interpolate", ["linear"], ["zoom"], 16, 0.1, 18, 0.2, 19, 0.5, 22, 1.5], "icon-image": ["step", ["zoom"], "crosswalk-small", 18, "crosswalk-large"], "icon-rotate": ["get", "direction"], "icon-rotation-alignment": "map", "icon-allow-overlap": true, "icon-ignore-placement": true, "visibility": "none"}, "paint": {}}, {"id": "ferry", "type": "line", "metadata": {"mapbox:featureComponent": "transit", "mapbox:group": "Transit, surface"}, "source": "composite", "source-layer": "road", "minzoom": 8, "filter": ["==", ["get", "type"], "ferry"], "layout": {"visibility": "none"}, "paint": {"line-color": ["interpolate", ["linear"], ["zoom"], 15, "hsla(198, 68%, 44%, 0)", 17, "hsla(223, 68%, 44%, 0)"], "line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 14, 0.5, 20, 1], "line-dasharray": ["step", ["zoom"], ["literal", [1, 0]], 13, ["literal", [12, 4]]]}}, {"id": "ferry-auto", "type": "line", "metadata": {"mapbox:featureComponent": "transit", "mapbox:group": "Transit, surface"}, "source": "composite", "source-layer": "road", "minzoom": 8, "filter": ["==", ["get", "type"], "ferry_auto"], "layout": {"visibility": "none"}, "paint": {"line-color": ["interpolate", ["linear"], ["zoom"], 15, "hsla(198, 68%, 44%, 0)", 17, "hsla(223, 68%, 44%, 0)"], "line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 14, 0.5, 20, 1]}}, {"id": "road-rail", "type": "line", "metadata": {"mapbox:featureComponent": "transit", "mapbox:group": "Transit, surface"}, "source": "composite", "source-layer": "road", "minzoom": 13, "filter": ["all", ["match", ["get", "class"], ["major_rail", "minor_rail"], true, false], ["match", ["get", "structure"], ["none", "ford"], true, false]], "layout": {"visibility": "none"}, "paint": {"line-color": ["interpolate", ["linear"], ["zoom"], 13, "hsla(236, 25%, 82%, 0)", 16, "hsl(220, 4%, 71%)"], "line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 14, 0.5, 20, 1]}}, {"id": "road-rail-tracks", "type": "line", "metadata": {"mapbox:featureComponent": "transit", "mapbox:group": "Transit, surface"}, "source": "composite", "source-layer": "road", "minzoom": 13, "filter": ["all", ["match", ["get", "class"], ["major_rail", "minor_rail"], true, false], ["match", ["get", "structure"], ["none", "ford"], true, false]], "layout": {"visibility": "none"}, "paint": {"line-color": ["interpolate", ["linear"], ["zoom"], 13, "hsla(236, 25%, 82%, 0)", 16, "hsl(220, 4%, 71%)"], "line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 14, 4, 20, 8], "line-dasharray": [0.1, 15], "line-opacity": ["interpolate", ["linear"], ["zoom"], 13.75, 0, 14, 1]}}, {"id": "bridge-path-bg", "type": "line", "metadata": {"mapbox:featureComponent": "walking-cycling", "mapbox:group": "Walking, cycling, etc., barriers-bridges"}, "source": "composite", "source-layer": "road", "minzoom": 14, "filter": ["all", ["==", ["get", "structure"], "bridge"], ["==", ["get", "class"], "path"], ["step", ["zoom"], ["!", ["match", ["get", "type"], ["steps", "sidewalk", "crossing"], true, false]], 16, ["!=", ["get", "type"], "steps"]], ["==", ["geometry-type"], "LineString"]], "layout": {"line-cap": ["step", ["zoom"], "butt", 14, "round"], "visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 15, 2, 18, 7], "line-color": "hsl(220, 11%, 79%)"}}, {"id": "bridge-steps-bg", "type": "line", "metadata": {"mapbox:featureComponent": "walking-cycling", "mapbox:group": "Walking, cycling, etc., barriers-bridges"}, "source": "composite", "source-layer": "road", "minzoom": 14, "filter": ["all", ["==", ["get", "type"], "steps"], ["==", ["get", "structure"], "bridge"], ["==", ["geometry-type"], "LineString"]], "layout": {"visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 15, 2, 17, 4.6, 18, 7], "line-color": "hsl(220, 11%, 79%)", "line-opacity": 0.75}}, {"id": "bridge-pedestrian-case", "type": "line", "metadata": {"mapbox:featureComponent": "walking-cycling", "mapbox:group": "Walking, cycling, etc., barriers-bridges"}, "source": "composite", "source-layer": "road", "minzoom": 14, "filter": ["all", ["==", ["get", "structure"], "bridge"], ["==", ["get", "class"], "pedestrian"], ["==", ["geometry-type"], "LineString"]], "layout": {"visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 14, 2, 18, 14.5], "line-color": "hsl(220, 20%, 85%)"}}, {"id": "bridge-path", "type": "line", "metadata": {"mapbox:featureComponent": "walking-cycling", "mapbox:group": "Walking, cycling, etc., barriers-bridges"}, "source": "composite", "source-layer": "road", "minzoom": 14, "filter": ["all", ["==", ["get", "structure"], "bridge"], ["==", ["get", "class"], "path"], ["!=", ["get", "type"], "steps"], ["==", ["geometry-type"], "LineString"]], "layout": {"visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 15, 1, 18, 4], "line-color": "hsl(0, 0%, 100%)", "line-dasharray": ["step", ["zoom"], ["literal", [4, 0.3]], 15, ["literal", [1.75, 0.3]], 16, ["literal", [1, 0.3]], 17, ["literal", [1, 0.25]]]}}, {"id": "bridge-steps", "type": "line", "metadata": {"mapbox:featureComponent": "walking-cycling", "mapbox:group": "Walking, cycling, etc., barriers-bridges"}, "source": "composite", "source-layer": "road", "minzoom": 14, "filter": ["all", ["==", ["get", "type"], "steps"], ["==", ["get", "structure"], "bridge"], ["==", ["geometry-type"], "LineString"]], "layout": {"visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 15, 1, 16, 1.6, 18, 6], "line-color": "hsl(0, 0%, 100%)", "line-dasharray": ["step", ["zoom"], ["literal", [1, 0]], 15, ["literal", [1.75, 1]], 16, ["literal", [1, 0.75]], 17, ["literal", [0.3, 0.3]]]}}, {"id": "bridge-pedestrian", "type": "line", "metadata": {"mapbox:featureComponent": "walking-cycling", "mapbox:group": "Walking, cycling, etc., barriers-bridges"}, "source": "composite", "source-layer": "road", "minzoom": 13, "filter": ["all", ["==", ["get", "structure"], "bridge"], ["==", ["get", "class"], "pedestrian"], ["==", ["geometry-type"], "LineString"]], "layout": {"visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 14, 0.5, 18, 12], "line-color": "hsl(0, 0%, 100%)", "line-dasharray": ["step", ["zoom"], ["literal", [2, 0.3]], 15, ["literal", [1, 0.3]], 16, ["literal", [1, 0.3]], 17, ["literal", [1, 0.25]]]}}, {"id": "bridge-minor-case", "type": "line", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, bridges"}, "source": "composite", "source-layer": "road", "minzoom": 13, "filter": ["all", ["==", ["get", "structure"], "bridge"], ["match", ["get", "class"], ["track"], true, "service", ["step", ["zoom"], false, 14, true], false], ["==", ["geometry-type"], "LineString"]], "layout": {"visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 14, 0.8, 22, 2], "line-color": "hsl(220, 20%, 85%)", "line-gap-width": ["interpolate", ["exponential", 1.5], ["zoom"], 14, 1, 18, 10, 22, 100]}}, {"id": "bridge-street-case", "type": "line", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, bridges"}, "source": "composite", "source-layer": "road", "minzoom": 14, "filter": ["all", ["==", ["get", "structure"], "bridge"], ["match", ["get", "class"], ["street", "street_limited"], true, false], ["==", ["geometry-type"], "LineString"]], "layout": {"visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 14, 0.8, 22, 2], "line-color": "hsl(220, 20%, 85%)", "line-gap-width": ["interpolate", ["exponential", 1.5], ["zoom"], 12, 0.5, 18, 20, 22, 200], "line-opacity": ["step", ["zoom"], 0, 14, 1]}}, {"id": "bridge-minor-link-case", "type": "line", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, bridges"}, "source": "composite", "source-layer": "road", "minzoom": 13, "filter": ["all", ["match", ["get", "class"], ["primary_link", "secondary_link", "tertiary_link"], true, false], ["==", ["get", "structure"], "bridge"], ["==", ["geometry-type"], "LineString"]], "layout": {"line-join": ["step", ["zoom"], "miter", 14, "round"], "visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 14, 0.8, 22, 2], "line-color": "hsl(220, 20%, 85%)", "line-gap-width": ["interpolate", ["exponential", 1.5], ["zoom"], 12, 0.4, 18, 18, 22, 180], "line-opacity": ["step", ["zoom"], 0, 11, 1]}}, {"id": "bridge-secondary-tertiary-case", "type": "line", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, bridges"}, "source": "composite", "source-layer": "road", "minzoom": 11, "filter": ["all", ["==", ["get", "structure"], "bridge"], ["match", ["get", "class"], ["secondary", "tertiary"], true, false], ["==", ["geometry-type"], "LineString"]], "layout": {"visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 14, 1, 22, 2], "line-color": "hsl(220, 20%, 85%)", "line-gap-width": ["interpolate", ["exponential", 1.5], ["zoom"], 3, 0, 18, 26, 22, 260], "line-opacity": ["step", ["zoom"], 0, 10, 1]}}, {"id": "bridge-primary-case", "type": "line", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, bridges"}, "source": "composite", "source-layer": "road", "minzoom": 10, "filter": ["all", ["==", ["get", "structure"], "bridge"], ["==", ["get", "class"], "primary"], ["==", ["geometry-type"], "LineString"]], "layout": {"visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 14, 1, 22, 2], "line-color": "hsl(220, 20%, 85%)", "line-gap-width": ["interpolate", ["exponential", 1.5], ["zoom"], 3, 0.8, 18, 28, 22, 280], "line-opacity": ["step", ["zoom"], 0, 10, 1]}}, {"id": "bridge-major-link-case", "type": "line", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, bridges"}, "source": "composite", "source-layer": "road", "minzoom": 12, "filter": ["all", ["==", ["get", "structure"], "bridge"], ["match", ["get", "class"], ["motorway_link", "trunk_link"], true, false], ["<=", ["get", "layer"], 1], ["==", ["geometry-type"], "LineString"]], "layout": {"visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 14, 0.8, 22, 2], "line-color": "hsl(220, 20%, 97%)", "line-gap-width": ["interpolate", ["exponential", 1.5], ["zoom"], 12, 0.8, 18, 20, 22, 200]}}, {"id": "bridge-motorway-trunk-case", "type": "line", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, bridges"}, "source": "composite", "source-layer": "road", "minzoom": 13, "filter": ["all", ["==", ["get", "structure"], "bridge"], ["match", ["get", "class"], ["motorway", "trunk"], true, false], ["<=", ["get", "layer"], 1], ["==", ["geometry-type"], "LineString"]], "layout": {"visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 14, 1, 22, 2], "line-color": "hsl(220, 20%, 97%)", "line-gap-width": ["interpolate", ["exponential", 1.5], ["zoom"], 3, 0.8, 18, 30, 22, 300]}}, {"id": "bridge-construction", "type": "line", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, bridges"}, "source": "composite", "source-layer": "road", "minzoom": 14, "filter": ["all", ["==", ["get", "structure"], "bridge"], ["==", ["get", "class"], "construction"], ["==", ["geometry-type"], "LineString"]], "layout": {"visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 14, 2, 18, 20, 22, 200], "line-color": "hsl(220, 20%, 85%)", "line-dasharray": ["step", ["zoom"], ["literal", [0.4, 0.8]], 15, ["literal", [0.3, 0.6]], 16, ["literal", [0.2, 0.3]], 17, ["literal", [0.2, 0.25]], 18, ["literal", [0.15, 0.15]]]}}, {"id": "bridge-minor", "type": "line", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, bridges"}, "source": "composite", "source-layer": "road", "minzoom": 13, "filter": ["all", ["==", ["get", "structure"], "bridge"], ["match", ["get", "class"], ["track"], true, "service", ["step", ["zoom"], false, 14, true], false], ["==", ["geometry-type"], "LineString"]], "layout": {"line-cap": ["step", ["zoom"], "butt", 14, "round"], "visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 14, 1, 18, 10, 22, 100], "line-color": "hsl(0, 0%, 100%)"}}, {"id": "bridge-minor-link", "type": "line", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, bridges"}, "source": "composite", "source-layer": "road", "minzoom": 13, "filter": ["all", ["match", ["get", "class"], ["primary_link", "secondary_link", "tertiary_link"], true, false], ["==", ["get", "structure"], "bridge"], ["==", ["geometry-type"], "LineString"]], "layout": {"line-cap": ["step", ["zoom"], "butt", 14, "round"], "visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 12, 0.4, 18, 18, 22, 180], "line-color": "hsl(0, 0%, 100%)"}}, {"id": "bridge-major-link", "type": "line", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, bridges"}, "source": "composite", "source-layer": "road", "minzoom": 12, "filter": ["all", ["==", ["get", "structure"], "bridge"], ["match", ["get", "class"], ["motorway_link", "trunk_link"], true, false], ["<=", ["get", "layer"], 1], ["==", ["geometry-type"], "LineString"]], "layout": {"line-cap": ["step", ["zoom"], "butt", 13, "round"], "visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 12, 0.8, 18, 20, 22, 200], "line-color": ["match", ["get", "class"], "motorway_link", "hsl(30, 100%, 70%)", "hsl(50, 89%, 70%)"]}}, {"id": "bridge-street", "type": "line", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, bridges"}, "source": "composite", "source-layer": "road", "minzoom": 13, "filter": ["all", ["==", ["get", "structure"], "bridge"], ["match", ["get", "class"], ["street", "street_limited"], true, false], ["==", ["geometry-type"], "LineString"]], "layout": {"line-cap": ["step", ["zoom"], "butt", 14, "round"], "visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 12, 0.5, 18, 20, 22, 200], "line-color": ["match", ["get", "class"], "street_limited", "hsla(221, 22%, 94%, 0)", "hsl(0, 0%, 100%)"], "line-opacity": ["step", ["zoom"], 0, 14, 1]}}, {"id": "bridge-street-low", "type": "line", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, bridges"}, "source": "composite", "source-layer": "road", "minzoom": 13, "maxzoom": 14, "filter": ["all", ["==", ["get", "structure"], "bridge"], ["match", ["get", "class"], ["street", "street_limited"], true, false], ["==", ["geometry-type"], "LineString"]], "layout": {"line-cap": ["step", ["zoom"], "butt", 14, "round"], "line-join": ["step", ["zoom"], "miter", 14, "round"], "visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 12, 0.5, 18, 20, 22, 200], "line-color": "hsl(0, 0%, 100%)"}}, {"id": "bridge-secondary-tertiary", "type": "line", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, bridges"}, "source": "composite", "source-layer": "road", "minzoom": 13, "filter": ["all", ["==", ["get", "structure"], "bridge"], ["match", ["get", "class"], ["secondary", "tertiary"], true, false], ["==", ["geometry-type"], "LineString"]], "layout": {"line-cap": ["step", ["zoom"], "butt", 14, "round"], "visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 3, 0, 18, 26, 22, 260], "line-color": "hsl(0, 0%, 100%)"}}, {"id": "bridge-primary", "type": "line", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, bridges"}, "source": "composite", "source-layer": "road", "minzoom": 13, "filter": ["all", ["==", ["get", "structure"], "bridge"], ["==", ["get", "class"], "primary"], ["==", ["geometry-type"], "LineString"]], "layout": {"line-cap": ["step", ["zoom"], "butt", 14, "round"], "visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 3, 0.8, 18, 28, 22, 280], "line-color": "hsl(0, 0%, 100%)"}}, {"id": "bridge-motorway-trunk", "type": "line", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, bridges"}, "source": "composite", "source-layer": "road", "minzoom": 13, "filter": ["all", ["==", ["get", "structure"], "bridge"], ["match", ["get", "class"], ["motorway", "trunk"], true, false], ["<=", ["get", "layer"], 1], ["==", ["geometry-type"], "LineString"]], "layout": {"line-cap": ["step", ["zoom"], "butt", 14, "round"], "visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 3, 0.8, 18, 30, 22, 300], "line-color": ["match", ["get", "class"], "motorway", "hsl(30, 100%, 70%)", "hsl(50, 89%, 70%)"]}}, {"id": "bridge-major-link-2-case", "type": "line", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, bridges"}, "source": "composite", "source-layer": "road", "minzoom": 12, "filter": ["all", ["==", ["get", "structure"], "bridge"], [">=", ["get", "layer"], 2], ["match", ["get", "class"], ["motorway_link", "trunk_link"], true, false], ["==", ["geometry-type"], "LineString"]], "layout": {"visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 14, 0.8, 22, 2], "line-color": "hsl(220, 20%, 97%)", "line-gap-width": ["interpolate", ["exponential", 1.5], ["zoom"], 12, 0.8, 18, 20, 22, 200]}}, {"id": "bridge-motorway-trunk-2-case", "type": "line", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, bridges"}, "source": "composite", "source-layer": "road", "minzoom": 13, "filter": ["all", ["==", ["get", "structure"], "bridge"], [">=", ["get", "layer"], 2], ["match", ["get", "class"], ["motorway", "trunk"], true, false], ["==", ["geometry-type"], "LineString"]], "layout": {"visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 14, 1, 22, 2], "line-color": "hsl(220, 20%, 97%)", "line-gap-width": ["interpolate", ["exponential", 1.5], ["zoom"], 3, 0.8, 18, 30, 22, 300]}}, {"id": "bridge-major-link-2", "type": "line", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, bridges"}, "source": "composite", "source-layer": "road", "minzoom": 12, "filter": ["all", ["==", ["get", "structure"], "bridge"], [">=", ["get", "layer"], 2], ["match", ["get", "class"], ["motorway_link", "trunk_link"], true, false], ["==", ["geometry-type"], "LineString"]], "layout": {"line-cap": ["step", ["zoom"], "butt", 13, "round"], "visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 12, 0.8, 18, 20, 22, 200], "line-color": ["match", ["get", "class"], "motorway_link", "hsl(30, 100%, 70%)", "hsl(50, 89%, 70%)"]}}, {"id": "bridge-motorway-trunk-2", "type": "line", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, bridges"}, "source": "composite", "source-layer": "road", "minzoom": 13, "filter": ["all", ["==", ["get", "structure"], "bridge"], [">=", ["get", "layer"], 2], ["match", ["get", "class"], ["motorway", "trunk"], true, false], ["==", ["geometry-type"], "LineString"]], "layout": {"line-cap": ["step", ["zoom"], "butt", 14, "round"], "visibility": "none"}, "paint": {"line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 3, 0.8, 18, 30, 22, 300], "line-color": ["match", ["get", "class"], "motorway", "hsl(30, 100%, 70%)", "hsl(50, 89%, 70%)"]}}, {"id": "bridge-oneway-arrow-blue", "type": "symbol", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, bridges"}, "source": "composite", "source-layer": "road", "minzoom": 16, "filter": ["all", ["==", ["get", "structure"], "bridge"], ["==", ["get", "oneway"], "true"], ["step", ["zoom"], ["match", ["get", "class"], ["primary", "secondary", "tertiary", "street", "street_limited"], true, false], 16, ["match", ["get", "class"], ["primary", "secondary", "tertiary", "street", "street_limited", "primary_link", "secondary_link", "tertiary_link", "service", "track"], true, false]], ["step", ["pitch"], true, 50, ["<", ["distance-from-center"], 1], 60, ["<", ["distance-from-center"], 1.5], 70, ["<", ["distance-from-center"], 2]]], "layout": {"symbol-placement": "line", "icon-image": ["step", ["zoom"], "oneway-small", 18, "oneway-large"], "symbol-spacing": 200, "icon-rotation-alignment": "map", "icon-allow-overlap": true, "icon-ignore-placement": true, "visibility": "none"}, "paint": {}}, {"id": "bridge-oneway-arrow-white", "type": "symbol", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, bridges"}, "source": "composite", "source-layer": "road", "minzoom": 16, "filter": ["all", ["==", ["get", "structure"], "bridge"], ["match", ["get", "class"], ["motorway", "trunk", "motorway_link", "trunk_link"], true, false], ["==", ["get", "oneway"], "true"], ["step", ["pitch"], true, 50, ["<", ["distance-from-center"], 1], 60, ["<", ["distance-from-center"], 1.5], 70, ["<", ["distance-from-center"], 2]]], "layout": {"symbol-placement": "line", "icon-image": "oneway-white-small", "symbol-spacing": 200, "icon-rotation-alignment": "map", "icon-allow-overlap": true, "icon-ignore-placement": true, "visibility": "none"}, "paint": {}}, {"id": "bridge-rail", "type": "line", "metadata": {"mapbox:featureComponent": "transit", "mapbox:group": "Transit, bridges"}, "source": "composite", "source-layer": "road", "minzoom": 13, "filter": ["all", ["==", ["get", "structure"], "bridge"], ["match", ["get", "class"], ["major_rail", "minor_rail"], true, false]], "layout": {"visibility": "none"}, "paint": {"line-color": ["interpolate", ["linear"], ["zoom"], 13, "hsla(236, 25%, 82%, 0)", 16, "hsl(220, 4%, 71%)"], "line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 14, 0.5, 20, 1]}}, {"id": "bridge-rail-tracks", "type": "line", "metadata": {"mapbox:featureComponent": "transit", "mapbox:group": "Transit, bridges"}, "source": "composite", "source-layer": "road", "minzoom": 13, "filter": ["all", ["==", ["get", "structure"], "bridge"], ["match", ["get", "class"], ["major_rail", "minor_rail"], true, false]], "layout": {"visibility": "none"}, "paint": {"line-color": ["interpolate", ["linear"], ["zoom"], 13, "hsla(236, 25%, 82%, 0)", 16, "hsl(220, 4%, 71%)"], "line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 14, 4, 20, 8], "line-dasharray": [0.1, 15], "line-opacity": ["interpolate", ["linear"], ["zoom"], 13.75, 0, 14, 1]}}, {"id": "aerialway", "type": "line", "metadata": {"mapbox:featureComponent": "transit", "mapbox:group": "Transit, bridges"}, "source": "composite", "source-layer": "road", "minzoom": 12, "filter": ["==", ["get", "class"], "aerialway"], "layout": {"visibility": "none"}, "paint": {"line-color": "hsl(225, 60%, 58%)", "line-width": ["interpolate", ["exponential", 1.5], ["zoom"], 14, 1, 20, 2], "line-dasharray": [4, 1]}}, {"id": "admin-1-boundary-bg", "type": "line", "metadata": {"mapbox:featureComponent": "admin-boundaries", "mapbox:group": "Administrative boundaries, admin"}, "source": "composite", "source-layer": "admin", "minzoom": 7, "filter": ["all", ["==", ["get", "admin_level"], 1], ["==", ["get", "maritime"], "false"], ["match", ["get", "worldview"], ["all", "CN"], true, false]], "paint": {"line-color": "hsl(240, 100%, 100%)", "line-width": ["interpolate", ["linear"], ["zoom"], 3, 3, 12, 6], "line-opacity": ["interpolate", ["linear"], ["zoom"], 7, 0, 8, 0.5], "line-dasharray": [1, 0], "line-blur": ["interpolate", ["linear"], ["zoom"], 3, 0, 12, 3]}}, {"id": "admin-0-boundary-bg", "type": "line", "metadata": {"mapbox:featureComponent": "admin-boundaries", "mapbox:group": "Administrative boundaries, admin"}, "source": "composite", "source-layer": "admin", "minzoom": 1, "filter": ["all", ["==", ["get", "admin_level"], 0], ["==", ["get", "maritime"], "false"], ["match", ["get", "worldview"], ["all", "CN"], true, false]], "paint": {"line-width": ["interpolate", ["linear"], ["zoom"], 3, 4, 12, 8], "line-color": "hsl(240, 100%, 100%)", "line-opacity": ["interpolate", ["linear"], ["zoom"], 3, 0, 4, 0.5], "line-blur": ["interpolate", ["linear"], ["zoom"], 3, 0, 12, 2]}}, {"id": "admin-1-boundary", "type": "line", "metadata": {"mapbox:featureComponent": "admin-boundaries", "mapbox:group": "Administrative boundaries, admin"}, "source": "composite", "source-layer": "admin", "minzoom": 2, "filter": ["all", ["==", ["get", "admin_level"], 1], ["==", ["get", "maritime"], "false"], ["match", ["get", "worldview"], ["all", "CN"], true, false]], "layout": {}, "paint": {"line-dasharray": ["step", ["zoom"], ["literal", [2, 0]], 7, ["literal", [2, 2, 6, 2]]], "line-width": ["interpolate", ["linear"], ["zoom"], 3, 0.3, 12, 1.5], "line-opacity": ["interpolate", ["linear"], ["zoom"], 2, 0, 3, 1], "line-color": "hsl(159, 56%, 42%)"}}, {"id": "admin-0-boundary", "type": "line", "metadata": {"mapbox:featureComponent": "admin-boundaries", "mapbox:group": "Administrative boundaries, admin"}, "source": "composite", "source-layer": "admin", "minzoom": 1, "filter": ["all", ["==", ["get", "admin_level"], 0], ["==", ["get", "disputed"], "false"], ["==", ["get", "maritime"], "false"], ["match", ["get", "worldview"], ["all", "CN"], true, false]], "layout": {}, "paint": {"line-color": "hsl(159, 56%, 42%)", "line-width": ["interpolate", ["linear"], ["zoom"], 3, 0.5, 12, 2], "line-dasharray": [10, 0]}}, {"id": "admin-0-boundary-disputed", "type": "line", "metadata": {"mapbox:featureComponent": "admin-boundaries", "mapbox:group": "Administrative boundaries, admin"}, "source": "composite", "source-layer": "admin", "minzoom": 1, "filter": ["all", ["==", ["get", "disputed"], "true"], ["==", ["get", "admin_level"], 0], ["==", ["get", "maritime"], "false"], ["match", ["get", "worldview"], ["all", "CN"], true, false]], "paint": {"line-color": "hsl(159, 56%, 42%)", "line-width": ["interpolate", ["linear"], ["zoom"], 3, 0.5, 12, 2], "line-dasharray": ["step", ["zoom"], ["literal", [3, 2, 5]], 7, ["literal", [2, 1.5]]]}}, {"id": "building-entrance", "type": "symbol", "metadata": {"mapbox:featureComponent": "buildings", "mapbox:group": "Buildings, building-labels"}, "source": "composite", "source-layer": "structure", "minzoom": 18, "filter": ["all", ["==", ["get", "class"], "entrance"], ["step", ["pitch"], true, 50, ["<", ["distance-from-center"], 1], 60, ["<", ["distance-from-center"], 1.5], 70, ["<", ["distance-from-center"], 2]]], "layout": {"icon-image": "marker", "text-field": ["get", "ref"], "text-size": 10, "text-offset": [0, -0.5], "text-font": ["DIN Pro Italic", "Arial Unicode MS Regular"], "visibility": "none"}, "paint": {"text-color": "hsla(221, 8%, 52%, 0)", "text-halo-color": "hsla(221, 13%, 92%, 0)", "text-halo-width": 1, "icon-opacity": 0.4}}, {"id": "building-number-label", "type": "symbol", "metadata": {"mapbox:featureComponent": "buildings", "mapbox:group": "Buildings, building-labels"}, "source": "composite", "source-layer": "housenum_label", "minzoom": 17, "filter": ["step", ["pitch"], true, 50, ["<", ["distance-from-center"], 1], 60, ["<", ["distance-from-center"], 1.5], 70, ["<", ["distance-from-center"], 2]], "layout": {"text-field": ["get", "house_num"], "text-font": ["DIN Pro Italic", "Arial Unicode MS Regular"], "text-padding": 4, "text-max-width": 7, "text-size": 10, "visibility": "none"}, "paint": {"text-color": "hsla(221, 8%, 52%, 0)", "text-halo-color": "hsla(221, 13%, 92%, 0)", "text-halo-width": 1}}, {"id": "block-number-label", "type": "symbol", "metadata": {"mapbox:featureComponent": "buildings", "mapbox:group": "Buildings, building-labels"}, "source": "composite", "source-layer": "place_label", "minzoom": 16, "filter": ["all", ["==", ["get", "class"], "settlement_subdivision"], ["==", ["get", "type"], "block"], ["step", ["pitch"], true, 50, ["<", ["distance-from-center"], 1], 60, ["<", ["distance-from-center"], 1.5], 70, ["<", ["distance-from-center"], 2]]], "layout": {"text-field": ["get", "name"], "text-font": ["DIN Pro Italic", "Arial Unicode MS Regular"], "text-max-width": 7, "text-size": 11, "visibility": "none"}, "paint": {"text-color": "hsla(221, 18%, 57%, 0)", "text-halo-color": "hsla(221, 17%, 100%, 0)", "text-halo-width": 0.5, "text-halo-blur": 0.5}}, {"id": "road-label", "type": "symbol", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, road-labels"}, "source": "composite", "source-layer": "road", "minzoom": 10, "filter": ["all", ["has", "name"], ["step", ["zoom"], ["match", ["get", "class"], ["motorway", "trunk", "primary", "secondary", "tertiary"], true, false], 12, ["match", ["get", "class"], ["motorway", "trunk", "primary", "secondary", "tertiary", "street", "street_limited"], true, false], 15, ["match", ["get", "class"], ["path", "pedestrian", "golf", "ferry", "aerialway"], false, true]], ["step", ["pitch"], true, 50, ["<", ["distance-from-center"], 2], 60, ["<", ["distance-from-center"], 2.5], 70, ["<", ["distance-from-center"], 3]]], "layout": {"text-size": ["interpolate", ["linear"], ["zoom"], 10, ["match", ["get", "class"], ["motorway", "trunk", "primary", "secondary", "tertiary"], 10, ["motorway_link", "trunk_link", "primary_link", "secondary_link", "tertiary_link", "street", "street_limited"], 9, 6.5], 18, ["match", ["get", "class"], ["motorway", "trunk", "primary", "secondary", "tertiary"], 16, ["motorway_link", "trunk_link", "primary_link", "secondary_link", "tertiary_link", "street", "street_limited"], 14, 13]], "text-max-angle": 30, "text-font": ["DIN Pro Regular", "Arial Unicode MS Regular"], "symbol-placement": "line", "text-padding": 5, "visibility": "none", "text-rotation-alignment": "map", "text-pitch-alignment": "viewport", "text-field": ["coalesce", ["get", "name_en"], ["get", "name"]], "text-letter-spacing": 0.01}, "paint": {"text-color": "hsl(0,0%, 0%)", "text-halo-color": ["match", ["get", "class"], ["motorway", "trunk"], "hsla(221, 25%, 100%, 0.75)", "hsla(221, 25%, 100%, 0)"], "text-halo-width": 1, "text-halo-blur": 1}}, {"id": "road-intersection", "type": "symbol", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, road-labels"}, "source": "composite", "source-layer": "road", "minzoom": 15, "filter": ["all", ["==", ["get", "class"], "intersection"], ["has", "name"], ["step", ["pitch"], true, 50, ["<", ["distance-from-center"], 1], 60, ["<", ["distance-from-center"], 1.5], 70, ["<", ["distance-from-center"], 2]]], "layout": {"text-field": ["coalesce", ["get", "name_en"], ["get", "name"]], "icon-image": "intersection", "icon-text-fit": "both", "icon-text-fit-padding": [1, 2, 1, 2], "text-size": ["interpolate", ["exponential", 1.2], ["zoom"], 15, 9, 18, 12], "text-font": ["DIN Pro Bold", "Arial Unicode MS Bold"], "visibility": "none"}, "paint": {"text-color": "hsl(230, 57%, 64%)"}}, {"id": "road-number-shield", "type": "symbol", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, road-labels"}, "source": "composite", "source-layer": "road", "minzoom": 6, "filter": ["all", ["case", ["has", "reflen"], ["<=", ["get", "reflen"], 6], ["has", "shield_beta"]], ["match", ["get", "class"], ["pedestrian", "service"], false, true], ["step", ["zoom"], ["==", ["geometry-type"], "Point"], 11, [">", ["get", "len"], 5000], 12, [">", ["get", "len"], 2500], 13, [">", ["get", "len"], 1000], 14, true], ["step", ["pitch"], true, 50, ["<", ["distance-from-center"], 2], 60, ["<", ["distance-from-center"], 2.5], 70, ["<", ["distance-from-center"], 3]]], "layout": {"text-size": 9, "icon-image": ["case", ["has", "shield_beta"], ["coalesce", ["image", ["concat", ["get", "shield_beta"], "-", ["to-string", ["get", "reflen"]]]], ["image", ["concat", ["get", "shield"], "-", ["to-string", ["get", "reflen"]]]], ["image", ["concat", "default-", ["to-string", ["get", "reflen"]]]]], ["concat", ["get", "shield"], "-", ["to-string", ["get", "reflen"]]]], "icon-rotation-alignment": "viewport", "text-max-angle": 38, "symbol-spacing": ["interpolate", ["linear"], ["zoom"], 11, 400, 14, 600], "text-font": ["DIN Pro Bold", "Arial Unicode MS Bold"], "symbol-placement": ["step", ["zoom"], "point", 11, "line"], "visibility": "none", "text-rotation-alignment": "viewport", "text-field": ["get", "ref"], "text-letter-spacing": 0.05}, "paint": {"text-color": ["case", ["has", "shield_beta"], ["case", ["all", ["has", "shield_text_color_beta"], ["to-boolean", ["coalesce", ["image", ["concat", ["get", "shield_beta"], "-", ["to-string", ["get", "reflen"]]]], ""]]], ["match", ["get", "shield_text_color_beta"], "white", "hsl(0, 0%, 100%)", "yellow", "hsl(50, 100%, 70%)", "orange", "hsl(25, 100%, 75%)", "blue", "hsl(230, 57%, 44%)", "red", "hsl(0, 87%, 59%)", "green", "hsl(140, 74%, 37%)", "hsl(230, 18%, 13%)"], "hsl(230, 18%, 13%)"], ["match", ["get", "shield_text_color"], "white", "hsl(0, 0%, 100%)", "yellow", "hsl(50, 100%, 70%)", "orange", "hsl(25, 100%, 75%)", "blue", "hsl(230, 57%, 44%)", "red", "hsl(0, 87%, 59%)", "green", "hsl(140, 74%, 37%)", "hsl(230, 18%, 13%)"]]}}, {"id": "road-exit-shield", "type": "symbol", "metadata": {"mapbox:featureComponent": "road-network", "mapbox:group": "Road network, road-labels"}, "source": "composite", "source-layer": "motorway_junction", "minzoom": 14, "filter": ["all", ["has", "reflen"], ["<=", ["get", "reflen"], 9], ["step", ["pitch"], true, 50, ["<", ["distance-from-center"], 1], 60, ["<", ["distance-from-center"], 1.5], 70, ["<", ["distance-from-center"], 2]]], "layout": {"text-field": ["get", "ref"], "text-size": 9, "icon-image": ["concat", "motorway-exit-", ["to-string", ["get", "reflen"]]], "text-font": ["DIN Pro Bold", "Arial Unicode MS Bold"], "visibility": "none"}, "paint": {"text-color": "hsl(0, 0%, 100%)", "text-translate": [0, 0]}}, {"id": "path-pedestrian-label", "type": "symbol", "metadata": {"mapbox:featureComponent": "walking-cycling", "mapbox:group": "Walking, cycling, etc., walking-cycling-labels"}, "source": "composite", "source-layer": "road", "minzoom": 12, "filter": ["all", ["case", ["has", "layer"], [">=", ["get", "layer"], 0], true], ["step", ["zoom"], ["match", ["get", "class"], ["pedestrian"], true, false], 15, ["match", ["get", "class"], ["path", "pedestrian"], true, false]], ["step", ["pitch"], true, 50, ["<", ["distance-from-center"], 1], 60, ["<", ["distance-from-center"], 1.5], 70, ["<", ["distance-from-center"], 2]]], "layout": {"text-size": ["interpolate", ["linear"], ["zoom"], 10, ["match", ["get", "class"], "pedestrian", 9, 6.5], 18, ["match", ["get", "class"], "pedestrian", 14, 13]], "text-max-angle": 30, "text-font": ["DIN Pro Regular", "Arial Unicode MS Regular"], "symbol-placement": "line", "text-padding": 1, "visibility": "none", "text-rotation-alignment": "map", "text-pitch-alignment": "viewport", "text-field": ["coalesce", ["get", "name_en"], ["get", "name"]], "text-letter-spacing": 0.01}, "paint": {"text-color": "hsl(0,0%, 0%)", "text-halo-color": "hsla(221, 25%, 100%, 0)", "text-halo-width": 1, "text-halo-blur": 1}}, {"id": "golf-hole-label", "type": "symbol", "metadata": {"mapbox:featureComponent": "walking-cycling", "mapbox:group": "Walking, cycling, etc., walking-cycling-labels"}, "source": "composite", "source-layer": "road", "minzoom": 16, "filter": ["all", ["==", ["get", "class"], "golf"], ["step", ["pitch"], true, 50, ["<", ["distance-from-center"], 1], 60, ["<", ["distance-from-center"], 1.5], 70, ["<", ["distance-from-center"], 2]]], "layout": {"text-field": ["coalesce", ["get", "name_en"], ["get", "name"]], "text-font": ["DIN Pro Medium", "Arial Unicode MS Regular"], "text-size": 12, "visibility": "none"}, "paint": {"text-halo-color": "rgb(19, 80, 7)", "text-halo-width": 0.5, "text-halo-blur": 0.5, "text-color": "hsl(110, 70%, 28%)"}}, {"id": "ferry-aerialway-label", "type": "symbol", "metadata": {"mapbox:featureComponent": "transit", "mapbox:group": "Transit, ferry-aerialway-labels"}, "source": "composite", "source-layer": "road", "minzoom": 15, "filter": ["all", ["match", ["get", "class"], "aerialway", true, "ferry", true, false], ["step", ["pitch"], true, 50, ["<", ["distance-from-center"], 1], 60, ["<", ["distance-from-center"], 1.5], 70, ["<", ["distance-from-center"], 2]]], "layout": {"text-size": ["interpolate", ["linear"], ["zoom"], 10, 6.5, 18, 13], "text-max-angle": 30, "text-font": ["DIN Pro Regular", "Arial Unicode MS Regular"], "symbol-placement": "line", "text-padding": 1, "visibility": "none", "text-rotation-alignment": "map", "text-pitch-alignment": "viewport", "text-field": ["coalesce", ["get", "name_en"], ["get", "name"]], "text-letter-spacing": 0.01}, "paint": {"text-color": ["match", ["get", "class"], "ferry", "hsla(189, 43%, 90%, 0)", "hsl(225, 60%, 58%)"], "text-halo-color": ["match", ["get", "class"], "ferry", "hsla(189, 75%, 51%, 0)", "hsla(221, 20%, 100%, 0)"], "text-halo-width": 1, "text-halo-blur": 1}}, {"id": "waterway-label", "type": "symbol", "metadata": {"mapbox:featureComponent": "natural-features", "mapbox:group": "Natural features, natural-labels"}, "source": "composite", "source-layer": "natural_label", "minzoom": 13, "filter": ["all", ["match", ["get", "class"], ["canal", "river", "stream", "disputed_canal", "disputed_river", "disputed_stream"], ["match", ["get", "worldview"], ["all", "US"], true, false], false], ["step", ["pitch"], true, 50, ["<", ["distance-from-center"], 1], 60, ["<", ["distance-from-center"], 1.5], 70, ["<", ["distance-from-center"], 2]], ["==", ["geometry-type"], "LineString"]], "layout": {"text-font": ["DIN Pro Italic", "Arial Unicode MS Regular"], "text-max-angle": 30, "symbol-spacing": ["interpolate", ["linear", 1], ["zoom"], 15, 250, 17, 400], "text-size": ["interpolate", ["linear"], ["zoom"], 13, 12, 18, 18], "symbol-placement": "line", "text-pitch-alignment": "viewport", "text-field": ["coalesce", ["get", "name_en"], ["get", "name"]], "visibility": "none"}, "paint": {"text-color": "hsla(189, 43%, 90%, 0)", "text-halo-color": "hsla(221, 17%, 100%, 0.5)"}}, {"id": "natural-line-label", "type": "symbol", "metadata": {"mapbox:featureComponent": "natural-features", "mapbox:group": "Natural features, natural-labels"}, "source": "composite", "source-layer": "natural_label", "minzoom": 4, "filter": ["all", ["match", ["get", "class"], ["glacier", "landform", "disputed_glacier", "disputed_landform"], ["match", ["get", "worldview"], ["all", "US"], true, false], false], ["<=", ["get", "filterrank"], 2], ["step", ["pitch"], true, 50, ["<", ["distance-from-center"], 1], 60, ["<", ["distance-from-center"], 1.5], 70, ["<", ["distance-from-center"], 2]], ["==", ["geometry-type"], "LineString"]], "layout": {"text-size": ["step", ["zoom"], ["step", ["get", "sizerank"], 18, 5, 12], 17, ["step", ["get", "sizerank"], 18, 13, 12]], "text-max-angle": 30, "text-field": ["coalesce", ["get", "name_en"], ["get", "name"]], "text-font": ["DIN Pro Medium", "Arial Unicode MS Regular"], "symbol-placement": "line-center", "text-pitch-alignment": "viewport", "visibility": "none"}, "paint": {"text-halo-width": 0.5, "text-halo-color": "hsla(221, 17%, 100%, 0)", "text-halo-blur": 0.5, "text-color": "hsl(210, 20%, 46%)"}}, {"id": "natural-point-label", "type": "symbol", "metadata": {"mapbox:featureComponent": "natural-features", "mapbox:group": "Natural features, natural-labels"}, "source": "composite", "source-layer": "natural_label", "minzoom": 4, "filter": ["all", ["match", ["get", "class"], ["dock", "glacier", "landform", "water_feature", "wetland", "disputed_dock", "disputed_glacier", "disputed_landform", "disputed_water_feature", "disputed_wetland"], ["match", ["get", "worldview"], ["all", "US"], true, false], false], ["<=", ["get", "filterrank"], 2], ["step", ["pitch"], true, 50, ["<", ["distance-from-center"], 1], 60, ["<", ["distance-from-center"], 1.5], 70, ["<", ["distance-from-center"], 2]], ["==", ["geometry-type"], "Point"]], "layout": {"text-size": ["step", ["zoom"], ["step", ["get", "sizerank"], 18, 5, 12], 17, ["step", ["get", "sizerank"], 18, 13, 12]], "icon-image": ["case", ["has", "maki_beta"], ["coalesce", ["image", ["get", "maki_beta"]], ["image", ["get", "maki"]]], ["image", ["get", "maki"]]], "text-font": ["DIN Pro Medium", "Arial Unicode MS Regular"], "text-offset": ["step", ["zoom"], ["step", ["get", "sizerank"], ["literal", [0, 0]], 5, ["literal", [0, 0.75]]], 17, ["step", ["get", "sizerank"], ["literal", [0, 0]], 13, ["literal", [0, 0.75]]]], "text-anchor": ["step", ["zoom"], ["step", ["get", "sizerank"], "center", 5, "top"], 17, ["step", ["get", "sizerank"], "center", 13, "top"]], "text-field": ["coalesce", ["get", "name_en"], ["get", "name"]], "visibility": "none"}, "paint": {"icon-opacity": ["step", ["zoom"], ["step", ["get", "sizerank"], 0, 5, 1], 17, ["step", ["get", "sizerank"], 0, 13, 1]], "text-halo-color": "hsla(221, 20%, 100%, 0)", "text-halo-width": 0.5, "text-halo-blur": 0.5, "text-color": "hsl(210, 20%, 46%)"}}, {"id": "water-line-label", "type": "symbol", "metadata": {"mapbox:featureComponent": "natural-features", "mapbox:group": "Natural features, natural-labels"}, "source": "composite", "source-layer": "natural_label", "minzoom": 1, "filter": ["all", ["match", ["get", "class"], ["bay", "ocean", "reservoir", "sea", "water", "disputed_bay", "disputed_ocean", "disputed_reservoir", "disputed_sea", "disputed_water"], ["match", ["get", "worldview"], ["all", "US"], true, false], false], ["step", ["pitch"], true, 50, ["<", ["distance-from-center"], 1], 60, ["<", ["distance-from-center"], 1.5], 70, ["<", ["distance-from-center"], 2]], ["==", ["geometry-type"], "LineString"]], "layout": {"text-size": ["interpolate", ["linear"], ["zoom"], 0, ["*", ["-", 16, ["sqrt", ["get", "sizerank"]]], 1], 22, ["*", ["-", 22, ["sqrt", ["get", "sizerank"]]], 1]], "text-max-angle": 30, "text-letter-spacing": ["match", ["get", "class"], "ocean", 0.25, ["sea", "bay"], 0.15, 0], "text-font": ["DIN Pro Italic", "Arial Unicode MS Regular"], "symbol-placement": "line-center", "text-pitch-alignment": "viewport", "text-field": ["coalesce", ["get", "name_en"], ["get", "name"]], "visibility": "none"}, "paint": {"text-color": ["match", ["get", "class"], ["bay", "ocean", "sea"], "hsla(189, 71%, 71%, 0)", "hsla(189, 43%, 90%, 0)"], "text-halo-color": "hsla(221, 17%, 100%, 0.5)"}}, {"id": "water-point-label", "type": "symbol", "metadata": {"mapbox:featureComponent": "natural-features", "mapbox:group": "Natural features, natural-labels"}, "source": "composite", "source-layer": "natural_label", "minzoom": 1, "filter": ["all", ["match", ["get", "class"], ["bay", "ocean", "reservoir", "sea", "water", "disputed_bay", "disputed_ocean", "disputed_reservoir", "disputed_sea", "disputed_water"], ["match", ["get", "worldview"], ["all", "US"], true, false], false], ["step", ["pitch"], true, 50, ["<", ["distance-from-center"], 1], 60, ["<", ["distance-from-center"], 1.5], 70, ["<", ["distance-from-center"], 2]], ["==", ["geometry-type"], "Point"]], "layout": {"text-line-height": 1.3, "text-size": ["interpolate", ["linear"], ["zoom"], 0, ["*", ["-", 16, ["sqrt", ["get", "sizerank"]]], 1], 22, ["*", ["-", 22, ["sqrt", ["get", "sizerank"]]], 1]], "text-font": ["DIN Pro Italic", "Arial Unicode MS Regular"], "text-field": ["coalesce", ["get", "name_en"], ["get", "name"]], "text-letter-spacing": ["match", ["get", "class"], "ocean", 0.25, ["bay", "sea"], 0.15, 0.01], "text-max-width": ["match", ["get", "class"], "ocean", 4, "sea", 5, ["bay", "water"], 7, 10], "visibility": "none"}, "paint": {"text-color": ["match", ["get", "class"], ["bay", "ocean", "sea"], "hsla(189, 71%, 71%, 0)", "hsla(189, 43%, 90%, 0)"], "text-halo-color": "hsla(221, 17%, 100%, 0.5)"}}, {"id": "poi-label", "type": "symbol", "metadata": {"mapbox:featureComponent": "point-of-interest-labels", "mapbox:group": "Point of interest labels, poi-labels"}, "source": "composite", "source-layer": "poi_label", "minzoom": 6, "filter": ["all", ["<=", ["get", "filterrank"], ["+", ["step", ["zoom"], 0, 16, 1, 17, 2], 3]], ["step", ["pitch"], true, 50, ["<", ["distance-from-center"], 2], 60, ["<", ["distance-from-center"], 2.5], 70, ["<", ["distance-from-center"], 3]]], "layout": {"text-size": ["step", ["zoom"], ["step", ["get", "sizerank"], 18, 5, 12], 17, ["step", ["get", "sizerank"], 18, 13, 12]], "icon-image": ["case", ["has", "maki_beta"], ["coalesce", ["image", ["get", "maki_beta"]], ["image", ["get", "maki"]]], ["image", ["get", "maki"]]], "text-font": ["DIN Pro Medium", "Arial Unicode MS Regular"], "text-offset": ["step", ["zoom"], ["step", ["get", "sizerank"], ["literal", [0, 0]], 5, ["literal", [0, 0.8]]], 17, ["step", ["get", "sizerank"], ["literal", [0, 0]], 13, ["literal", [0, 0.8]]]], "text-anchor": ["step", ["zoom"], ["step", ["get", "sizerank"], "center", 5, "top"], 17, ["step", ["get", "sizerank"], "center", 13, "top"]], "text-field": ["coalesce", ["get", "name_en"], ["get", "name"]], "visibility": "none"}, "paint": {"icon-opacity": ["step", ["zoom"], ["step", ["get", "sizerank"], 0, 5, 1], 17, ["step", ["get", "sizerank"], 0, 13, 1]], "text-halo-color": "hsla(221, 20%, 100%, 0)", "text-halo-width": 0.5, "text-halo-blur": 0.5, "text-color": ["match", ["get", "class"], "food_and_drink", "hsl(40, 95%, 43%)", "park_like", "hsl(110, 70%, 28%)", "education", "hsl(30, 50%, 43%)", "medical", "hsl(0, 70%, 58%)", "sport_and_leisure", "hsl(190, 60%, 48%)", ["store_like", "food_and_drink_stores"], "hsl(210, 70%, 58%)", ["commercial_services", "motorist", "lodging"], "hsl(260, 70%, 63%)", ["arts_and_entertainment", "historic", "landmark"], "hsl(320, 70%, 63%)", "hsl(210, 20%, 46%)"]}}, {"id": "transit-label", "type": "symbol", "metadata": {"mapbox:featureComponent": "transit", "mapbox:group": "Transit, transit-labels"}, "source": "composite", "source-layer": "transit_stop_label", "minzoom": 12, "filter": ["all", ["step", ["zoom"], ["all", ["<=", ["get", "filterrank"], 4], ["match", ["get", "mode"], "rail", true, "metro_rail", true, false], ["!=", ["get", "stop_type"], "entrance"]], 14, ["all", ["match", ["get", "mode"], "rail", true, "metro_rail", true, false], ["!=", ["get", "stop_type"], "entrance"]], 15, ["all", ["match", ["get", "mode"], "rail", true, "metro_rail", true, "ferry", true, "light_rail", true, false], ["!=", ["get", "stop_type"], "entrance"]], 16, ["all", ["match", ["get", "mode"], "bus", false, true], ["!=", ["get", "stop_type"], "entrance"]], 17, ["!=", ["get", "stop_type"], "entrance"], 19, true], ["step", ["pitch"], true, 50, ["<", ["distance-from-center"], 1], 60, ["<", ["distance-from-center"], 1.5], 70, ["<", ["distance-from-center"], 2]]], "layout": {"text-size": 12, "icon-image": ["get", "network"], "text-font": ["DIN Pro Medium", "Arial Unicode MS Regular"], "text-justify": ["match", ["get", "stop_type"], "entrance", "left", "center"], "visibility": "none", "text-offset": ["match", ["get", "stop_type"], "entrance", ["literal", [1, 0]], ["literal", [0, 0.8]]], "text-anchor": ["match", ["get", "stop_type"], "entrance", "left", "top"], "text-field": ["step", ["zoom"], "", 13, ["match", ["get", "mode"], ["rail", "metro_rail"], ["coalesce", ["get", "name_en"], ["get", "name"]], ""], 14, ["match", ["get", "mode"], ["bus", "bicycle"], "", ["coalesce", ["get", "name_en"], ["get", "name"]]], 18, ["coalesce", ["get", "name_en"], ["get", "name"]]], "text-letter-spacing": 0.01, "text-max-width": ["match", ["get", "stop_type"], "entrance", 15, 9]}, "paint": {"text-halo-color": "hsla(221, 20%, 100%, 0)", "text-color": ["match", ["get", "network"], "tokyo-metro", "hsl(180, 50%, 30%)", "mexico-city-metro", "hsl(25, 100%, 63%)", ["barcelona-metro", "delhi-metro", "hong-kong-mtr", "milan-metro", "osaka-subway"], "hsl(0, 90%, 47%)", ["boston-t", "washington-metro"], "hsl(230, 18%, 20%)", ["chongqing-rail-transit", "kiev-metro", "singapore-mrt", "taipei-metro"], "hsl(140, 90%, 25%)", "hsl(225, 60%, 58%)"], "text-halo-blur": 0.5, "text-halo-width": 0.5}}, {"id": "airport-label", "type": "symbol", "metadata": {"mapbox:featureComponent": "transit", "mapbox:group": "Transit, transit-labels"}, "source": "composite", "source-layer": "airport_label", "minzoom": 8, "filter": ["all", ["match", ["get", "class"], ["military", "civil", "disputed_military", "disputed_civil"], ["match", ["get", "worldview"], ["all", "US"], true, false], false], ["step", ["pitch"], true, 50, ["<", ["distance-from-center"], 3], 60, ["<", ["distance-from-center"], 4], 70, ["<", ["distance-from-center"], 5]]], "layout": {"text-line-height": 1.1, "text-size": ["step", ["get", "sizerank"], 18, 9, 12], "icon-image": ["get", "maki"], "text-font": ["DIN Pro Medium", "Arial Unicode MS Regular"], "visibility": "none", "text-offset": [0, 0.8], "text-rotation-alignment": "viewport", "text-anchor": "top", "text-field": ["step", ["get", "sizerank"], ["case", ["has", "ref"], ["concat", ["get", "ref"], " -\n", ["coalesce", ["get", "name_en"], ["get", "name"]]], ["coalesce", ["get", "name_en"], ["get", "name"]]], 15, ["get", "ref"]], "text-letter-spacing": 0.01, "text-max-width": 9}, "paint": {"text-color": "hsl(225, 60%, 58%)", "text-halo-color": "hsla(221, 20%, 100%, 0)", "text-halo-width": 1}}, {"id": "state-label", "type": "symbol", "metadata": {"mapbox:featureComponent": "place-labels", "mapbox:group": "Place labels, place-labels"}, "source": "composite", "source-layer": "place_label", "minzoom": 3, "maxzoom": 9, "filter": ["all", ["match", ["get", "class"], ["state", "disputed_state"], ["match", ["get", "worldview"], ["all", "CN"], true, false], false], ["step", ["pitch"], true, 50, ["<", ["distance-from-center"], 3], 60, ["<", ["distance-from-center"], 4], 70, ["<", ["distance-from-center"], 5]]], "layout": {"text-size": ["interpolate", ["cubic-bezier", 0.85, 0.7, 0.65, 1], ["zoom"], 4, ["step", ["get", "symbolrank"], 9, 6, 8, 7, 7], 9, ["step", ["get", "symbolrank"], 21, 6, 16, 7, 14]], "text-transform": "uppercase", "text-font": ["DIN Pro Bold", "Arial Unicode MS Bold"], "text-field": ["coalesce", ["get", "name_zh-<PERSON>"], ["get", "name"]], "text-letter-spacing": 0.15, "text-max-width": 6}, "paint": {"text-color": "hsl(220, 30%, 0%)", "text-halo-color": "hsla(221, 25%, 100%, 0)", "text-halo-width": 1, "text-opacity": 0.5}}, {"id": "country-label", "type": "symbol", "metadata": {"mapbox:featureComponent": "place-labels", "mapbox:group": "Place labels, place-labels"}, "source": "composite", "source-layer": "place_label", "minzoom": 1, "maxzoom": 10, "filter": ["all", ["match", ["get", "class"], ["country", "disputed_country"], ["match", ["get", "worldview"], ["all", "CN"], true, false], false], ["step", ["pitch"], true, 50, ["<", ["distance-from-center"], 3], 60, ["<", ["distance-from-center"], 4], 70, ["<", ["distance-from-center"], 5]]], "layout": {"icon-image": "", "text-field": ["coalesce", ["get", "name_zh-<PERSON>"], ["get", "name"]], "text-line-height": 1.1, "text-max-width": 6, "text-font": ["DIN Pro Medium", "Arial Unicode MS Regular"], "text-radial-offset": ["step", ["zoom"], 0.6, 8, 0], "text-justify": ["step", ["zoom"], ["match", ["get", "text_anchor"], ["left", "bottom-left", "top-left"], "left", ["right", "bottom-right", "top-right"], "right", "center"], 7, "auto"], "text-size": ["interpolate", ["cubic-bezier", 0.2, 0, 0.7, 1], ["zoom"], 1, ["step", ["get", "symbolrank"], 11, 4, 9, 5, 8], 9, ["step", ["get", "symbolrank"], 22, 4, 19, 5, 17]]}, "paint": {"icon-opacity": ["step", ["zoom"], ["case", ["has", "text_anchor"], 1, 0], 7, 0], "text-color": "hsl(220, 30%, 0%)", "text-halo-color": ["interpolate", ["linear"], ["zoom"], 2, "hsla(221, 25%, 100%, 0.75)", 3, "hsla(221, 25%, 100%, 0)"], "text-halo-width": 1.25}}], "created": "2025-06-12T02:22:03.323Z", "modified": "2025-06-12T02:22:03.323Z", "id": "cmbsr6jaz011g01sd03vc9g1x", "owner": "bar5xc", "visibility": "private", "protected": false, "draft": false}